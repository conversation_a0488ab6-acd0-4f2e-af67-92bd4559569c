package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/********************************
 * @title EquipmentAddForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 新增仪器设备入参
 *
 * <AUTHOR>
 * @date 2025/6/15 10:30
 * @version 0.0.1
 *********************************/
@ApiModel(value = "新增仪器设备入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EquipmentAddForm {
    /**
     * 仪器设备名称
     */
    @ApiModelProperty(value = "仪器设备名称", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String equipmentName;

    /**
     * 仪器用途
     */
    @ApiModelProperty(value = "仪器用途", required = true)
    @NotEmpty(message = "javax.validation.constraints.NotEmpty.message")
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_USAGE,
        message = "{com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.message}",
        enableWriteBack = true,
        writeBackDictDataList = "usageDictDataList"
    )
    private List<String> usage;

    /**
     * 品牌
     */
    @ApiModelProperty(value = "品牌", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String brand;

    /**
     * 型号
     */
    @ApiModelProperty(value = "型号")
    private String model;

    /**
     * 仪器所属（0：自有，1：租赁）
     */
    @ApiModelProperty(value = "仪器所属（0：自有，1：租赁）", required = true)
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_OWNERSHIP_STATUS,
        message = "仪器所属值无效",
        enableWriteBack = true,
        writeBackDictData = "ownershipStatusDictData"
    )
    private Integer ownershipStatus;

    /**
     * 检测能力
     */
    @ApiModelProperty(value = "检测能力", required = true)
    @NotEmpty(message = "javax.validation.constraints.NotEmpty.message")
    private List<Long> inspectItem;

    /**
     * 收货人
     */
    @ApiModelProperty(value = "收货人", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String consignee;

    /**
     * 收货日期 yyyy-MM-dd
     */
    @ApiModelProperty(value = "收货日期 yyyy-MM-dd", required = true)
    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    private String receiptDate;

    /**
     * 文件记录id
     */
    @ApiModelProperty(value = "文件记录id")
    private List<Long> fileRecordIds;

    /**
     * 仪器用途列表
     */
    @ApiModelProperty(value = "仪器用途列表", hidden = true)
    @JsonIgnore
    private List<DictData> usageDictDataList;

    /**
     * 仪器所属
     */
    @ApiModelProperty(value = "仪器所属", hidden = true)
    @JsonIgnore
    private DictData ownershipStatusDictData;
}
