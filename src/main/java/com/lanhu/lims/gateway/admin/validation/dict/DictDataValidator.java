package com.lanhu.lims.gateway.admin.validation.dict;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.model.DictData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/********************************
 * @title DictDataValidator
 * @package com.lanhu.lims.gateway.admin.validation.dict
 * @description 字典数据验证器，支持验证和数据回写
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
@Slf4j
@Component
public class DictDataValidator implements ConstraintValidator<DictDataValidation, Object> {
    
    @Resource
    private DictDataMapper dictDataMapper;
    
    private DictDataValidation annotation;
    
    @Override
    public void initialize(DictDataValidation annotation) {
        this.annotation = annotation;
    }
    
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        // 如果允许空值且值为空，则验证通过
        if (annotation.allowEmpty() && ObjectUtil.isEmpty(value)) {
            return true;
        }
        
        // 如果不允许空值且值为空，则验证失败
        if (!annotation.allowEmpty() && ObjectUtil.isEmpty(value)) {
            return false;
        }
        
        String valueStr = String.valueOf(value);
        if (StrUtil.isBlank(valueStr)) {
            return annotation.allowEmpty();
        }
        
        try {
            // 解析多值
            List<String> values = parseValues(valueStr, annotation.delimiter());
            
            // 验证字典值
            ValidationResult result = validateDictValues(values, annotation.dictType());
            
            // 如果启用数据回写且验证通过
            if (annotation.enableWriteBack() && result.isValid() && annotation.writeBack().length > 0) {
                performWriteBack(context, result.getValidData());
            }
            
            return result.isValid();
            
        } catch (Exception e) {
            log.error("字典数据验证失败: value={}, dictType={}", value, annotation.dictType(), e);
            return false;
        }
    }
    
    /**
     * 解析多值字符串
     */
    public List<String> parseValues(String value, String delimiter) {
        if (StrUtil.isBlank(value)) {
            return new ArrayList<>();
        }
        
        if (StrUtil.isBlank(delimiter)) {
            return Collections.singletonList(value.trim());
        }
        
        return Arrays.stream(value.split(delimiter))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证字典值
     */
    @DS("slave_1")
    public ValidationResult validateDictValues(List<String> values, String dictType) {
        if (CollUtil.isEmpty(values)) {
            return ValidationResult.success(new ArrayList<>());
        }
        
        try {
            // 查询有效的字典数据
            List<DictData> validDictData = dictDataMapper.selectValidDictDataByTypeAndValues(dictType, values);
            
            if (CollUtil.isEmpty(validDictData)) {
                return ValidationResult.failure("未找到有效的字典数据");
            }
            
            // 检查所有值是否都有效
            Set<String> validValues = validDictData.stream()
                    .map(DictData::getDictValue)
                    .collect(Collectors.toSet());
            
            List<String> invalidValues = values.stream()
                    .filter(v -> !validValues.contains(v))
                    .collect(Collectors.toList());
            
            if (CollUtil.isNotEmpty(invalidValues)) {
                return ValidationResult.failure("无效的字典值: " + String.join(", ", invalidValues));
            }
            
            return ValidationResult.success(validDictData);
            
        } catch (Exception e) {
            log.error("查询字典数据失败: dictType={}, values={}", dictType, values, e);
            return ValidationResult.failure("字典数据查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行数据回写
     */
    private void performWriteBack(ConstraintValidatorContext context, List<DictData> dictDataList) {
        if (CollUtil.isEmpty(dictDataList) || annotation.writeBack().length == 0) {
            return;
        }
        
        try {
            // 获取当前验证的对象实例
            Object targetObject = getCurrentValidationTarget(context);
            if (targetObject == null) {
                if (annotation.writeBackRequired()) {
                    throw new RuntimeException("无法获取验证目标对象，数据回写失败");
                }
                return;
            }
            
            // 执行数据回写
            writeBackData(targetObject, dictDataList);
            
        } catch (Exception e) {
            log.error("数据回写失败", e);
            if (annotation.writeBackRequired()) {
                throw new RuntimeException("数据回写失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取当前验证的目标对象
     * 注意：这是一个技术限制，Bean Validation框架不直接提供访问验证对象的方式
     * 这里使用ThreadLocal来传递对象引用
     */
    private Object getCurrentValidationTarget(ConstraintValidatorContext context) {
        // 从ThreadLocal获取当前验证的对象
        return ValidationContextHolder.getCurrentTarget();
    }
    
    /**
     * 回写数据到目标对象
     */
    private void writeBackData(Object target, List<DictData> dictDataList) {
        if (CollUtil.isEmpty(dictDataList)) {
            return;
        }
        
        // 使用第一条数据进行回写
        DictData dictData = dictDataList.get(0);
        
        for (String mapping : annotation.writeBack()) {
            String[] parts = mapping.split(":");
            if (parts.length != 2) {
                continue;
            }
            
            String sourceField = parts[0].trim();
            String targetField = parts[1].trim();
            
            try {
                Object value = getFieldValue(dictData, sourceField);
                if (value != null) {
                    ReflectUtil.setFieldValue(target, targetField, value);
                    log.debug("数据回写成功: {} -> {} = {}", sourceField, targetField, value);
                }
            } catch (Exception e) {
                log.warn("数据回写失败: {} -> {}, error: {}", sourceField, targetField, e.getMessage());
                if (annotation.writeBackRequired()) {
                    throw new RuntimeException("数据回写失败: " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 获取字典数据字段值
     */
    private Object getFieldValue(DictData dictData, String fieldName) {
        switch (fieldName.toLowerCase()) {
            case "dict_label":
                return dictData.getDictLabel();
            case "dict_label_en":
                return dictData.getDictLabelEn();
            case "dict_value":
                return dictData.getDictValue();
            case "dict_sort":
                return dictData.getDictSort();
            case "remark":
                return dictData.getRemark();
            default:
                // 使用反射获取字段值
                try {
                    return ReflectUtil.getFieldValue(dictData, fieldName);
                } catch (Exception e) {
                    log.warn("获取字典数据字段值失败: fieldName={}", fieldName);
                    return null;
                }
        }
    }
    
    /**
     * 验证结果封装类
     */
    public static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private List<DictData> validData;
        
        private ValidationResult(boolean valid, String errorMessage, List<DictData> validData) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.validData = validData;
        }
        
        public static ValidationResult success(List<DictData> validData) {
            return new ValidationResult(true, null, validData);
        }
        
        public static ValidationResult failure(String errorMessage) {
            return new ValidationResult(false, errorMessage, null);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public List<DictData> getValidData() {
            return validData;
        }
    }
}
