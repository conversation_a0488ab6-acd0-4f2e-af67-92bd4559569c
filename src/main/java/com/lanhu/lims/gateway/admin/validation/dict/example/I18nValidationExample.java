package com.lanhu.lims.gateway.admin.validation.dict.example;

import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/********************************
 * @title I18nValidationExample
 * @package com.lanhu.lims.gateway.admin.validation.dict.example
 * @description 国际化字典验证示例表单
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
@Data
@ApiModel(description = "国际化字典验证示例表单")
public class I18nValidationExample {
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "{javax.validation.constraints.NotNull.message}")
    private Long userId;
    
    /**
     * 用户状态 - 使用通用国际化消息
     */
    @ApiModelProperty(value = "用户状态", required = true)
    @NotBlank(message = "{javax.validation.constraints.NotBlank.message}")
    @DictDataValidation(
        dictType = DictDataTypeEnum.USER_STATUS,
        message = "{com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.message}",
        enableWriteBack = true,
        writeBackDictData = "statusDictData"
    )
    private String status;
    
    /**
     * 用户性别 - 使用特定的国际化消息
     */
    @ApiModelProperty(value = "用户性别")
    @DictDataValidation(
        dictType = DictDataTypeEnum.USER_GENDER,
        message = "{com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.user.gender.message}",
        enableWriteBack = true,
        writeBackDictData = "genderDictData",
        allowEmpty = true
    )
    private String gender;
    
    /**
     * 设备用途 - 使用特定的国际化消息
     */
    @ApiModelProperty(value = "设备用途")
    @NotEmpty(message = "{javax.validation.constraints.NotEmpty.message}")
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_USAGE,
        message = "{com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.usage.message}",
        enableWriteBack = true,
        writeBackDictDataList = "usageDictDataList"
    )
    private List<String> usage;
    
    /**
     * 设备所属 - 使用特定的国际化消息
     */
    @ApiModelProperty(value = "设备所属", required = true)
    @NotNull(message = "{javax.validation.constraints.NotNull.message}")
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_OWNERSHIP_STATUS,
        message = "{com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.ownership.message}",
        enableWriteBack = true,
        writeBackDictData = "ownershipDictData"
    )
    private Integer ownership;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    
    // ==================== 回写字段 ====================
    
    /**
     * 状态字典数据
     */
    @ApiModelProperty(value = "状态字典数据", hidden = true)
    private DictData statusDictData;
    
    /**
     * 性别字典数据
     */
    @ApiModelProperty(value = "性别字典数据", hidden = true)
    private DictData genderDictData;
    
    /**
     * 用途字典数据列表
     */
    @ApiModelProperty(value = "用途字典数据列表", hidden = true)
    private List<DictData> usageDictDataList;
    
    /**
     * 所属字典数据
     */
    @ApiModelProperty(value = "所属字典数据", hidden = true)
    private DictData ownershipDictData;
}

/**
 * 国际化配置示例：
 * 
 * 1. 在 messages_zh_CN.properties 中：
 * javax.validation.constraints.NotNull.message = 字段 {0} 为空
 * javax.validation.constraints.NotBlank.message = 字段 {0} 为空
 * javax.validation.constraints.NotEmpty.message = 字段 {0} 为空
 * com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.message = 字典数据验证失败
 * com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.user.gender.message = 用户性别值无效
 * com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.usage.message = 仪器用途值无效
 * com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.ownership.message = 仪器所属值无效
 * 
 * 2. 在 messages_en_US.properties 中：
 * javax.validation.constraints.NotNull.message = Field {0} is empty
 * javax.validation.constraints.NotBlank.message = Field {0} is empty
 * javax.validation.constraints.NotEmpty.message = Field {0} is empty
 * com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.message = Dictionary data validation failed
 * com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.user.gender.message = Invalid user gender value
 * com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.usage.message = Invalid equipment usage value
 * com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.ownership.message = Invalid equipment ownership value
 * 
 * 使用效果：
 * 
 * 1. 中文环境下验证失败：
 * {
 *   "code": 400,
 *   "message": "仪器用途值无效"
 * }
 * 
 * 2. 英文环境下验证失败：
 * {
 *   "code": 400,
 *   "message": "Invalid equipment usage value"
 * }
 * 
 * 优势：
 * 1. 统一的国际化管理：所有验证消息都在properties文件中管理
 * 2. 类型安全：使用枚举避免字符串拼写错误
 * 3. 灵活配置：可以为不同字典类型配置特定的错误消息
 * 4. 标准规范：完全符合Bean Validation的国际化规范
 * 5. 易于维护：修改消息只需要修改properties文件
 */
