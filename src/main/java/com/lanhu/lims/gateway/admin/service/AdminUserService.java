package com.lanhu.lims.gateway.admin.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lanhu.lims.gateway.admin.annotation.LhTransaction;
import com.lanhu.lims.gateway.admin.auth.service.TokenService;
import com.lanhu.lims.gateway.admin.auth.utils.AuthUtil;
import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.PcsResultCode;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.core.Result;
import com.lanhu.lims.gateway.admin.enums.*;
import com.lanhu.lims.gateway.admin.file.service.IFileService;
import com.lanhu.lims.gateway.admin.mapper.*;
import com.lanhu.lims.gateway.admin.model.*;
import com.lanhu.lims.gateway.admin.utils.DictDataUtil;
import com.lanhu.lims.gateway.admin.vo.req.*;
import com.lanhu.lims.gateway.admin.vo.resp.AdminUserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/5/21 11:28 上午
 */

@Slf4j
@Service
public class AdminUserService {



    @Autowired
    private AdminUserMapper adminUserMapper;



    @Autowired
    private AdminUserTokenMapper adminUserTokenMapper;

    @Autowired
    private MenuMapper menuMapper;




    @Autowired
    private AdminUserRoleMapper adminUserRoleMapper;


    @Autowired
    private RoleMapper roleMapper;


    @Autowired
    private TokenService tokenService;


    @Autowired
    private CaptchaMapper captchaMapper;


    @Autowired
    private DictDataService dictDataService;

    @Autowired
    private DeptService deptService;

    /**
     * @description: 用户名登录
     * @param: [userLoginForm]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult<com.lanhu.imenu.gateway.pc.vo.resp.UserDetailVO>
     * @author: liuyi
     * @date: 9:01 上午 2023/1/13
     */
    @DS("master_1")
    public PcsResult<LoginUser> loginByUserName(UserLoginForm userLoginForm) {


        LambdaQueryWrapper<Captcha> wrapper1 = Wrappers.lambdaQuery();
        wrapper1.eq(Captcha::getUid, userLoginForm.getUid());
        wrapper1.eq(Captcha::getCaptcha,userLoginForm.getCaptcha());
        wrapper1.gt(Captcha::getExpireTime, DateUtil.date());
        wrapper1.eq(Captcha::getUseCounter,0);
        wrapper1.last(" limit 1");


        Captcha captcha = captchaMapper.selectOne(wrapper1);


        if(captcha == null){
            return Result.error(PcsResultCode.CAPTCHA_NOT_EXIST);
        }



        String md5Pwd = SecureUtil.md5(userLoginForm.getPwd());


        LambdaQueryWrapper<AdminUser> wrapper = Wrappers.lambdaQuery();

        wrapper.and(
                wq -> wq.eq(AdminUser::getUserName,userLoginForm.getUserName())
                .or().eq(AdminUser::getEmail, userLoginForm.getUserName())
             );


        AdminUser adminUser = adminUserMapper.selectOne(wrapper);

        if (ObjectUtil.isNull(adminUser)) {
            return Result.error(PcsResultCode.PLA_USER_NOT_EXISTS);
        }

        if(StringUtils.isBlank(adminUser.getPassword()) || !md5Pwd.equalsIgnoreCase(adminUser.getPassword())){
            return Result.error(PcsResultCode.PLA_USER_LOGIN_PASSWORD_ERROR);
        }

        //如果账号被禁用
        if (adminUser.getStatus() != AdminUserStatusEnum.NORMAL.getCode() ) {
            return Result.error(PcsResultCode.PLA_USER_CAN_NOT_LOGIN);
        }

        //如果账号不存在
        if(adminUser.getIsEffect() == IsEffectEnum.DELETE.getCode()){
            return Result.error(PcsResultCode.PLA_USER_NOT_EXISTS);
        }



        // 组装登录信息
        LoginUser loginUser =  tokenService.assemblyLoginUser(adminUser,true);

        AdminUserToken token = AdminUserToken.builder()
                .id(IdUtil.getSnowflakeNextId())
                .isEffect(IsEffectEnum.NORMAL.getCode())
                .createTime(DateUtil.date())
                .token(String.valueOf(loginUser.getUserId()))
                .updateTime(DateUtil.date())
                .osVersion(userLoginForm.getOsVersion())
                .build();

        adminUserTokenMapper.insert(token);


        //token写缓存
        tokenService.saveToken(loginUser.getToken(),String.valueOf(loginUser.getUserId()));



        Captcha updateCaptcha = Captcha.builder()
                .id(captcha.getId())
                .useCounter(1)
                .build();

        //设置成该验证码已使用
        captchaMapper.updateById(updateCaptcha);


        //更新登录时间
        AdminUser adminUserUpdate = AdminUser.builder()
                .id(adminUser.getId())
                .loginTime(new Date())
                .build();

        adminUserMapper.updateById(adminUserUpdate);

        return  Result.ok(loginUser);
    }







    /**
     * @description:
     * @param: [userModifyPwdForm]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 5:22 下午 2023/1/12
     */
    @DS("master_1")
    public PcsResult userPwdModify(AdminUser pcUser, UserModifyPwdForm userModifyPwdForm){


        String oldpwd = SecureUtil.md5(userModifyPwdForm.getOldPassword());

        //旧密码验证
        if(!oldpwd.equalsIgnoreCase(pcUser.getPassword())){
            return  Result.error(PcsResultCode.PLA_USER_PWD_NOTSAME_ERROR);
        }



        String newpwd = SecureUtil.md5(userModifyPwdForm.getNewPassword());


        AdminUser updatePlaPcUser = AdminUser.builder()
                .id(pcUser.getId())
                .password(newpwd)
                .updateTime(new Date())
                .updateBy(pcUser.getId())
                .build();


        //修改密码
        adminUserMapper.updateById(updatePlaPcUser);



        return Result.ok();
    }




    /**
     * @description: 用户退出
     * @param: []
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 9:02 上午 2023/1/13
     */
    public PcsResult logout(){

        AuthUtil.logout();

        return Result.ok();
    }



    /**
     * @description:
     * @param: [userModifyPwdForm]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 5:22 下午 2023/1/12
     */
    @DS("master_1")
    public PcsResult frozen(AdminUser pcUser, UserFrozenForm userFrozenForm){


        AdminUser updatePlaPcUser = AdminUser.builder()
                .id(userFrozenForm.getUserId())
                .updateTime(new Date())
                .status(userFrozenForm.getStatus())
                .updateBy(pcUser.getUpdateBy())
                .build();


        //修改密码
        adminUserMapper.updateById(updatePlaPcUser);



        return Result.ok();
    }


    /**
     * @description:
     * @param: [userModifyPwdForm]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 5:22 下午 2023/1/12
     */
    @DS("master_1")
    public PcsResult enable(AdminUser pcUser, UserEnableForm userEnableForm){





        AdminUser updatePlaPcUser = AdminUser.builder()
                .id(userEnableForm.getUserId())
                .updateTime(new Date())
                .isEffect(userEnableForm.getIsEffect())
                .updateBy(pcUser.getUpdateBy())
                .build();


        //修改密码
        adminUserMapper.updateById(updatePlaPcUser);



        return Result.ok();
    }

    /**
     * @description:
     * @param: [userModifyPwdForm]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 5:22 下午 2023/1/12
     */
    @DS("master_1")
    public PcsResult userPwdReset(AdminUser pcUser, UserResetPwdForm userResetPwdForm){


        String resetPwd = SecureUtil.md5(SecureUtil.md5(ProjectConstant.USER_RESET_PWD));


        AdminUser updatePlaPcUser = AdminUser.builder()
                .id(userResetPwdForm.getUserId())
                .updateTime(new Date())
                .password(resetPwd)
                .updateBy(pcUser.getUpdateBy())
                .build();


        //修改密码
        adminUserMapper.updateById(updatePlaPcUser);



        return Result.ok();
    }



    /**
     * @description: 分页查询
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 8:21 下午 2023/1/15
     */
    @DS("slave_1")
    public PcsResult<IPage<AdminUser>> listPage(AdminUser plaPcUser, UserListPageForm request) {


        LambdaQueryWrapper<AdminUser> wrapper = Wrappers.lambdaQuery();


        if(StringUtils.isNotBlank(request.getInfo())){


            wrapper.and(wq -> wq.like(AdminUser::getUserName, request.getInfo())
                    .or().like(AdminUser::getRealName,request.getInfo())
            );
        }



        if(request.getStatus() != null){
            wrapper.eq(AdminUser::getStatus,request.getStatus());

        }


//        if (request.getDeptId()!=null){
//            wrapper.eq(AdminUser::getDeptId,request.getDeptId());
//        }




        List<Long> userIds = Lists.newArrayList();


        if(CollectionUtil.isNotEmpty(request.getRoleIds())){
            LambdaQueryWrapper<AdminUserRole> wrapper1 = Wrappers.lambdaQuery();
            wrapper1.in(AdminUserRole::getRoleId,request.getRoleIds());


            List<AdminUserRole> plaPcUserRoleList = adminUserRoleMapper.selectList(wrapper1);

            if(CollectionUtil.isNotEmpty(plaPcUserRoleList)){
                userIds = plaPcUserRoleList.stream().map(p->p.getAdminUserId()).collect(Collectors.toList());
            }else {
                userIds.add(IdUtil.getSnowflakeNextId());
            }

        }

        if(CollectionUtil.isNotEmpty(userIds)){
            wrapper.in(AdminUser::getId,userIds);
        }


        wrapper.eq(AdminUser::getIsEffect, IsEffectEnum.NORMAL.getCode());


        // 默认根据addTime 倒序排列
        wrapper.orderByDesc(AdminUser::getCreateTime);

        Page<AdminUser> page = new Page<>(request.getPageIndex(), request.getPageSize());


        IPage<AdminUser> pageList = adminUserMapper.selectPage(page, wrapper);


        //查看用户拥有的所有角色
        List<Role> roleList = roleMapper.selectUserRoleList(null);

        Map<Long,List<Role>> userRoleMap = Maps.newHashMap();

        if(CollectionUtil.isNotEmpty(roleList)){
            userRoleMap =  roleList.stream().collect(Collectors.groupingBy(Role::getUserId));
        }


        //部门列表
        List<Dept> deptList = deptService.list();



        //字典数据
        List<DictData> dictDataList = dictDataService.list();

        if(CollectionUtil.isNotEmpty(pageList.getRecords())){

            for (AdminUser record : pageList.getRecords()) {


                List<Role> list = userRoleMap.get(record.getId());

                String roleName = ProjectConstant.EMPTY;

                if(CollectionUtil.isNotEmpty(list)){
                    roleName = String.join(ProjectConstant.COMMA,list.stream().map(Role::getRoleName).collect(Collectors.toList()));
                }


                record.setRoleName(roleName);

                //检测项目翻译
                record.setInspectItem(DictDataUtil.valueTranslation(record.getInspectItem(), DictDataTypeEnum.INSPECT_ITEM,ProjectConstant.LEFT_SLASH,dictDataList));


                record.setRoleName(roleName);

                //部门名称翻译
                record.setDeptName(deptService.deptNameTranslation(deptList,record.getDeptId()));

            }

        }




        return Result.ok(pageList);

    }



    /**
     * @description: 所有正常用户列表查询
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 8:21 下午 2023/1/15
     */
    @DS("slave_1")
    public PcsResult<List<AdminUser>> list(AdminUser plaPcUser, UserListForm request) {


        LambdaQueryWrapper<AdminUser> wrapper = Wrappers.lambdaQuery();


        if(StringUtils.isNotBlank(request.getInfo())){

            wrapper.and(wq -> wq.like(AdminUser::getUserName, request.getInfo())
                    .or().like(AdminUser::getRealName,request.getInfo())
            );
        }



        if(request.getWorkStatus() != null){
            wrapper.eq(AdminUser::getWorkStatus,request.getWorkStatus());
        }


        wrapper.eq(AdminUser::getIsEffect, IsEffectEnum.NORMAL.getCode());


        // 默认根据addTime 倒序排列
        wrapper.orderByDesc(AdminUser::getRealName);




        List<AdminUser> adminUserList =  adminUserMapper.selectList(wrapper);



        return Result.ok(adminUserList);

    }


    /**
     * @description: 用户详情
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult<com.lanhu.imenu.gateway.pc.model.pla.PlaPcUser>
     * @author: liuyi
     * @date: 3:53 下午 2023/1/16
     */
    @DS("slave_1")
    public PcsResult<AdminUserVO> detail(AdminUser plaPcUser, UserDetailForm request){

        AdminUser select = adminUserMapper.selectById(request.getUserId());

        if(select == null){
            return  Result.error(PcsResultCode.PLA_USER_NOT_EXISTS);
        }

        //字典数据
        List<DictData> dictDataList = dictDataService.list();

        AdminUserVO adminUserVO = new AdminUserVO();

        BeanUtil.copyProperties(select,adminUserVO);

        //我的角色
        List<Role> checkRoleList =  roleMapper.selectRoleListByUserId(request.getUserId());

        List<Long> checkRoleIdList = Lists.newArrayList();

        if(CollectionUtil.isNotEmpty(checkRoleList)){
            checkRoleIdList = checkRoleList.stream().map(Role::getRoleId).collect(Collectors.toList());
        }



        //所有角色
        List<Role> roleList = roleMapper.selectAll();

        if(CollectionUtil.isNotEmpty(roleList)){

            for (Role role : roleList) {

                if(checkRoleIdList.contains(role.getRoleId())){
                    role.setChecked(true);
                }


            }

        }

        adminUserVO.setRolesList(roleList);



        return  Result.ok(adminUserVO);

    }




    /**
     * @description: 用户添加
     * @param: [request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date:8 下午 2023/1/16
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult add(AdminUser adminUser, UserAddForm request){


        LambdaQueryWrapper<AdminUser> wrapper = Wrappers.lambdaQuery();
//        wrapper.eq(AdminUser::getEmployeeId,request.getEmployeeId());
//        wrapper.eq(AdminUser::getIsEffect, IsEffectEnum.NORMAL.getCode());


        //工号判断
//        AdminUser select = adminUserMapper.selectOne(wrapper);
//
//        if(select != null){
//            return Result.error(PcsResultCode.PLA_USER_EMPLOYEE_ID_EXISTS);
//        }

        //邮箱绑定判断
//        wrapper = Wrappers.lambdaQuery();
//        wrapper.eq(AdminUser::getEmail,request.getEmail());
//        wrapper.eq(AdminUser::getIsEffect, IsEffectEnum.NORMAL.getCode());
//        select = adminUserMapper.selectOne(wrapper);
//
//        if(select != null){
//            return Result.error(PcsResultCode.PLA_USER_EMAIL_BIND_EXISTS);
//        }

        //用户名判断
        wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AdminUser::getUserName,request.getUserName());
        AdminUser select = adminUserMapper.selectOne(wrapper);

        if(select != null){
            return Result.error(PcsResultCode.PLA_USER_NAME_BIND_EXISTS);
        }


        String md5Pwd = SecureUtil.md5(request.getPwd());


        AdminUser addUser = AdminUser.builder()
                .id(IdUtil.getSnowflakeNextId())
                .loginTime(new Date())
                .createTime(new Date())
                .updateTime(new Date())
                .isEffect(IsEffectEnum.NORMAL.getCode())
                .email(ProjectConstant.EMPTY)
                .mobile(ProjectConstant.EMPTY)
                .status(request.getStatus())
                .userName(request.getUserName())
                .password(md5Pwd)
                .createBy(adminUser.getId())
                .updateBy(adminUser.getId())
                .realName(request.getRealName())
                .deptId(request.getDeptId())
                .absenceEndDate(ProjectConstant.EMPTY)
                .absenceStartDate(ProjectConstant.EMPTY)
                .employeeId(ProjectConstant.EMPTY)
                .gender(request.getGender())
                .workStatus(WorkStatusEnum.EMPLOYED.getCode())
                .inspectItem(request.getInspectItem())
                .build();


        adminUserMapper.insert(addUser);



        Set<Long> roleIdList = Sets.newHashSet();




        if(CollectionUtil.isNotEmpty(request.getRoleIds())){
            roleIdList.addAll(request.getRoleIds());
        }

        List<AdminUserRole> userRoleList = Lists.newArrayList();




        if(CollectionUtil.isNotEmpty(roleIdList)){

            for (Long roleId : roleIdList) {
                AdminUserRole plaPcUserRole = AdminUserRole.builder()
                        .roleId(roleId)
                        .adminUserId(addUser.getId()).build();


                userRoleList.add(plaPcUserRole);
            }


            adminUserRoleMapper.batchInsert(userRoleList);
        }







        return Result.ok();

    }


    /**
     * @description: 用户编辑
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 3:54 下午 2023/1/16
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult edit(AdminUser plaPcUser, UserEditForm request){

        AdminUser select = adminUserMapper.selectById(request.getUserId());

        if(select == null){
            return  Result.error(PcsResultCode.PLA_USER_NOT_EXISTS);
        }

//        LambdaQueryWrapper<AdminUser> wrapper = Wrappers.lambdaQuery();
//        wrapper.eq(AdminUser::getEmployeeId,request.getEmployeeId());
//        wrapper.ne(AdminUser::getId,request.getUserId());
//        wrapper.eq(AdminUser::getIsEffect, IsEffectEnum.NORMAL.getCode());
//
//
//
//        //工号判断
//        AdminUser judge = adminUserMapper.selectOne(wrapper);
//
//        if(judge != null){
//            return Result.error(PcsResultCode.PLA_USER_EMPLOYEE_ID_EXISTS);
//        }

        //邮箱绑定判断
//        wrapper = Wrappers.lambdaQuery();
//        wrapper.eq(AdminUser::getEmail,request.getEmail());
//        wrapper.ne(AdminUser::getId,request.getUserId());
//        wrapper.eq(AdminUser::getIsEffect, IsEffectEnum.NORMAL.getCode());
//
//        judge = adminUserMapper.selectOne(wrapper);
//
//        if(judge != null){
//            return Result.error(PcsResultCode.PLA_USER_EMAIL_BIND_EXISTS);
//        }

        //用户名判断
        LambdaQueryWrapper<AdminUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AdminUser::getUserName,request.getUserName());
        wrapper.ne(AdminUser::getId,request.getUserId());
        AdminUser judge = adminUserMapper.selectOne(wrapper);

        if(judge != null){
            return Result.error(PcsResultCode.PLA_USER_NAME_BIND_EXISTS);
        }




        AdminUser update = AdminUser.builder()
                .id(request.getUserId())
                .updateTime(new Date())
                .updateBy(plaPcUser.getId())
                .status(request.getStatus())
                .realName(request.getRealName())
                .deptId(request.getDeptId())
                .inspectItem(request.getInspectItem())
                .gender(request.getGender())
                .build();


        adminUserMapper.updateById(update);




        Set<Long> roleIdList = Sets.newHashSet();




        if(CollectionUtil.isNotEmpty(request.getRoleIds())){
            roleIdList.addAll(request.getRoleIds());
        }

        List<AdminUserRole> userRoleList = Lists.newArrayList();


        adminUserRoleMapper.deleteByUserId(request.getUserId());




        if(CollectionUtil.isNotEmpty(roleIdList)){

            for (Long roleId : roleIdList) {
                AdminUserRole plaPcUserRole = AdminUserRole.builder()
                        .roleId(roleId)
                        .adminUserId(request.getUserId())
                        .build();
                userRoleList.add(plaPcUserRole);
            }


            adminUserRoleMapper.batchInsert(userRoleList);
        }







        return Result.ok();







    }



    /**
     * @description: 用户休假设置
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 3:54 下午 2023/1/16
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult vacationSetting(AdminUser plaPcUser, UserVacationSettingForm request){

        //替岗人
        AdminUser substituteUser = adminUserMapper.selectById(request.getSubstituteUserId());

        if(substituteUser == null){
            return  Result.error(PcsResultCode.PLA_USER_NOT_EXISTS);
        }



        AdminUser update = AdminUser.builder()
                .id(plaPcUser.getId())
                .updateTime(new Date())
                .updateBy(plaPcUser.getId())
                .absenceStartDate(request.getAbsenceStartDate())
                .absenceEndDate(request.getAbsenceEndDate())
                .substituteUserId(request.getSubstituteUserId())
                .substituteUserName(substituteUser.getRealName())
                .build();


        adminUserMapper.updateById(update);




        return Result.ok();







    }


    /**
     * @description: 用户离职设置
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 3:54 下午 2023/1/16
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult resignUpdate(AdminUser plaPcUser, UserResignSettingForm request){

        AdminUser select = adminUserMapper.selectById(request.getUserId());

        if(select == null){
            return  Result.error(PcsResultCode.PLA_USER_NOT_EXISTS);
        }


        AdminUser update = AdminUser.builder()
                .id(request.getUserId())
                .updateTime(new Date())
                .updateBy(plaPcUser.getId())
                .terminationDate(request.getTerminationDate())
                .status(EnableEnum.DISABLE.getCode())
                .workStatus(WorkStatusEnum.DIMISSION.getCode())
                .build();


        adminUserMapper.updateById(update);




        return Result.ok();







    }


    /**
     * @description: 用户检测项设置
     * @param: [plaPcUser, request]
     * @return: com.lanhu.imenu.gateway.pc.core.PcsResult
     * @author: liuyi
     * @date: 3:54 下午 2023/1/16
     */
    @DS("master_1")
    @LhTransaction
    public PcsResult inspectItemUpdate(AdminUser plaPcUser, UserInspectItemSettingForm request){

        AdminUser select = adminUserMapper.selectById(request.getUserId());

        if(select == null){
            return  Result.error(PcsResultCode.PLA_USER_NOT_EXISTS);
        }

        //字典数据
        List<DictData> dictDataList = dictDataService.list();

        if(!DictDataUtil.checkExist(request.getInspectItem(),DictDataTypeEnum.INSPECT_ITEM,ProjectConstant.LEFT_SLASH,dictDataList)){
            return Result.ok(PcsResultCode.DictDataNotExist);
        }


        AdminUser update = AdminUser.builder()
                .id(request.getUserId())
                .updateTime(new Date())
                .updateBy(plaPcUser.getId())
                .inspectItem(request.getInspectItem())
                .build();


        adminUserMapper.updateById(update);




        return Result.ok();







    }



    @DS("master_1")
    @LhTransaction
    public PcsResult batchDel(AdminUser adminUser, UserBatchDelForm request){


        List<Long> userIdList = Arrays.asList(request.getUserIds().split(ProjectConstant.COMMA)).stream().map(r->Long.valueOf(r)).collect(Collectors.toList());

        List<AdminUser> updateList = Lists.newArrayList();

        for (Long aLong : userIdList) {
            AdminUser update = AdminUser.builder()
                    .updateBy(adminUser.getId())
                    .updateTime(new Date())
                    .id(aLong)
                    .isEffect(IsEffectEnum.DELETE.getCode())
                    .build();

            updateList.add(update);

        }



        adminUserMapper.updateBatchSelective(updateList);



        return Result.ok();

    }


    @DS("master_1")
    @LhTransaction
    public PcsResult batchFrozen(AdminUser adminUser, UserBatchFrozenForm request){


        List<Long> userIdList = Arrays.asList(request.getUserIds().split(ProjectConstant.COMMA)).stream().map(r->Long.valueOf(r)).collect(Collectors.toList());

        List<AdminUser> updateList = Lists.newArrayList();

        for (Long aLong : userIdList) {
            AdminUser update = AdminUser.builder()
                    .updateBy(adminUser.getId())
                    .updateTime(new Date())
                    .id(aLong)
                    .status(AdminUserStatusEnum.DISABLE.getCode())
                    .build();

            updateList.add(update);

        }



        adminUserMapper.updateBatchSelective(updateList);



        return Result.ok();

    }



}
