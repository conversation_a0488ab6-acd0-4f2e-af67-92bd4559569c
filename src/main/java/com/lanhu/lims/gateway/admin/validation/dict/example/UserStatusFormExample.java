package com.lanhu.lims.gateway.admin.validation.dict.example;

import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/********************************
 * @title UserStatusFormExample
 * @package com.lanhu.lims.gateway.admin.validation.dict.example
 * @description 用户状态表单示例 - 使用枚举的字典验证和数据回写
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
@Data
@ApiModel(description = "用户状态表单示例")
public class UserStatusFormExample {
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 用户状态 - 使用枚举的字典验证和数据回写
     */
    @ApiModelProperty(value = "用户状态", required = true)
    @NotBlank(message = "用户状态不能为空")
    @DictDataValidation(
        dictType = DictDataTypeEnum.USER_STATUS,
        message = "用户状态值无效",
        enableWriteBack = true,
        writeBackDictData = "statusDictData",
        writeBack = {"dict_label:statusName", "dict_label_en:statusNameEn"}
    )
    private String status;
    
    /**
     * 用户性别 - 使用枚举的字典验证
     */
    @ApiModelProperty(value = "用户性别")
    @DictDataValidation(
        dictType = DictDataTypeEnum.USER_GENDER,
        message = "用户性别值无效",
        enableWriteBack = true,
        writeBackDictData = "genderDictData",
        allowEmpty = true
    )
    private String gender;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    
    // ==================== 回写字段 ====================
    
    /**
     * 状态字典数据 - 会被自动回写
     */
    @ApiModelProperty(value = "状态字典数据", hidden = true)
    private DictData statusDictData;
    
    /**
     * 状态名称 - 会被自动回写
     */
    @ApiModelProperty(value = "状态名称", hidden = true)
    private String statusName;
    
    /**
     * 状态名称（英文） - 会被自动回写
     */
    @ApiModelProperty(value = "状态名称（英文）", hidden = true)
    private String statusNameEn;
    
    /**
     * 性别字典数据 - 会被自动回写
     */
    @ApiModelProperty(value = "性别字典数据", hidden = true)
    private DictData genderDictData;
}

/**
 * 使用示例：
 * 
 * 1. 在Controller中：
 * @PostMapping("/user/status/update")
 * public Result updateUserStatus(@RequestBody @Valid UserStatusFormExample form) {
 *     // 此时字典相关字段已经被自动填充
 *     DictData statusDict = form.getStatusDictData();
 *     String statusLabel = statusDict.getDictLabel();
 *     
 *     return Result.ok(form);
 * }
 * 
 * 2. 前端传入：
 * {
 *   "userId": 123,
 *   "status": "1",
 *   "gender": "1",
 *   "remark": "更新用户状态"
 * }
 * 
 * 3. 后端接收到的数据：
 * {
 *   "userId": 123,
 *   "status": "1",
 *   "statusDictData": {
 *     "id": 101,
 *     "dictValue": "1",
 *     "dictLabel": "启用",
 *     "dictLabelEn": "Enable"
 *   },
 *   "statusName": "启用",
 *   "statusNameEn": "Enable",
 *   "gender": "1",
 *   "genderDictData": {
 *     "id": 201,
 *     "dictValue": "1",
 *     "dictLabel": "男",
 *     "dictLabelEn": "Male"
 *   },
 *   "remark": "更新用户状态"
 * }
 * 
 * 优势：
 * 1. 类型安全：使用枚举避免字符串拼写错误
 * 2. IDE支持：自动补全和重构支持
 * 3. 编译时检查：枚举值在编译时验证
 * 4. 维护性：枚举集中管理所有字典类型
 */
