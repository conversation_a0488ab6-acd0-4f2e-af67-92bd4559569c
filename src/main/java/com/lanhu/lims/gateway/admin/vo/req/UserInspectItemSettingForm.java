package com.lanhu.lims.gateway.admin.vo.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/1/16 10:53 上午
 */
@ApiModel(value = "用户检测项设置入参")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInspectItemSettingForm {


    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "javax.validation.constraints.NotNull.message")
    private Long userId;

    @NotBlank(message = "javax.validation.constraints.NotBlank.message")
    @ApiModelProperty(value = "检测项，多个以按/分隔")
    private String inspectItem;






}
