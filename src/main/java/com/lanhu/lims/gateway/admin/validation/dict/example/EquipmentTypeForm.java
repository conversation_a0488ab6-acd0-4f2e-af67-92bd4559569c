package com.lanhu.lims.gateway.admin.validation.dict.example;

import com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/********************************
 * @title EquipmentTypeForm
 * @package com.lanhu.lims.gateway.admin.validation.dict.example
 * @description 设备类型表单示例 - 多值字典验证和数据回写
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
@Data
@ApiModel(description = "设备类型表单示例")
public class EquipmentTypeForm {
    
    /**
     * 设备编号
     */
    @ApiModelProperty(value = "设备编号", required = true)
    @NotBlank(message = "设备编号不能为空")
    private String equipmentCode;
    
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", required = true)
    @NotBlank(message = "设备名称不能为空")
    private String equipmentName;
    
    /**
     * 设备用途 - 多值字典验证
     * 支持多个用途，用逗号分隔，如："0,1,2"
     */
    @ApiModelProperty(value = "设备用途（多个用逗号分隔）", required = true)
    @NotBlank(message = "设备用途不能为空")
    @DictDataValidation(
        dictType = "equipment_usage",
        delimiter = ",",
        message = "设备用途值无效",
        enableWriteBack = true,
        writeBack = {"dict_label:usageNames"},
        writeBackRequired = false
    )
    private String usage;
    
    /**
     * 用途名称 - 会被自动回写（多个名称用逗号分隔）
     */
    @ApiModelProperty(value = "用途名称")
    private String usageNames;
    
    /**
     * 设备状态 - 单值字典验证
     */
    @ApiModelProperty(value = "设备状态", required = true)
    @NotBlank(message = "设备状态不能为空")
    @DictDataValidation(
        dictType = "equipment_status",
        message = "设备状态值无效",
        enableWriteBack = true,
        writeBack = {"dict_label:statusName", "dict_label_en:statusNameEn"},
        writeBackRequired = false
    )
    private String status;
    
    /**
     * 状态名称 - 会被自动回写
     */
    @ApiModelProperty(value = "状态名称")
    private String statusName;
    
    /**
     * 状态名称（英文） - 会被自动回写
     */
    @ApiModelProperty(value = "状态名称（英文）")
    private String statusNameEn;
}

/**
 * 使用示例：
 * 
 * 1. 在Controller中：
 * @PostMapping("/equipment/add")
 * public Result addEquipment(@RequestBody @Valid EquipmentTypeForm form) {
 *     // 此时字典相关字段已经被自动填充
 *     return Result.ok(form);
 * }
 * 
 * 2. 前端传入：
 * {
 *   "equipmentCode": "EQ001",
 *   "equipmentName": "高效液相色谱仪",
 *   "usage": "0,1",      // 检测,分析
 *   "status": "1"        // 正常
 * }
 * 
 * 3. 后端接收到的数据：
 * {
 *   "equipmentCode": "EQ001",
 *   "equipmentName": "高效液相色谱仪",
 *   "usage": "0,1",
 *   "usageNames": "检测,分析",     // 自动回写
 *   "status": "1",
 *   "statusName": "正常",         // 自动回写
 *   "statusNameEn": "Normal"     // 自动回写
 * }
 * 
 * 4. 验证失败示例：
 * 前端传入：
 * {
 *   "equipmentCode": "EQ001",
 *   "equipmentName": "高效液相色谱仪",
 *   "usage": "0,999",    // 999是无效值
 *   "status": "1"
 * }
 * 响应：400 Bad Request - "设备用途值无效"
 */
