package com.lanhu.lims.gateway.admin.validation.dict;

import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/********************************
 * @title DictDataValidation
 * @package com.lanhu.lims.gateway.admin.validation.dict
 * @description 字典数据验证注解，支持验证和数据回写
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DictDataValidator.class)
public @interface DictDataValidation {
    
    /**
     * 字典类型枚举值
     */
    DictDataTypeEnum dictType();
    
    /**
     * 分隔符，用于多值验证
     * 支持：逗号(,)、分号(;)、竖线(|)、斜杠(/)、空格( )等
     */
    String delimiter() default ",";
    
    /**
     * 是否允许空值
     */
    boolean allowEmpty() default true;
    
    /**
     * 验证失败时的错误消息
     */
    String message() default "字典数据验证失败";
    
    /**
     * 数据回写配置
     * 格式：查询结果字段名:实体字段名
     * 例如：{"dict_label:statusName", "dict_label_en:statusNameEn"}
     */
    String[] writeBack() default {};

    /**
     * 是否启用数据回写功能
     */
    boolean enableWriteBack() default false;

    /**
     * 回写到DictData对象的字段名
     * 当验证单个值时，将查询到的DictData对象回写到指定字段
     * 例如：writeBackDictData = "ownershipStatusDictData"
     */
    String writeBackDictData() default "";

    /**
     * 回写到List<DictData>的字段名
     * 当验证多个值时，将查询到的DictData列表回写到指定字段
     * 例如：writeBackDictDataList = "usageDictDataList"
     */
    String writeBackDictDataList() default "";
    
    /**
     * 回写失败时是否抛出异常
     */
    boolean writeBackRequired() default false;
    
    /**
     * 验证分组
     */
    Class<?>[] groups() default {};
    
    /**
     * 负载
     */
    Class<? extends Payload>[] payload() default {};
}
