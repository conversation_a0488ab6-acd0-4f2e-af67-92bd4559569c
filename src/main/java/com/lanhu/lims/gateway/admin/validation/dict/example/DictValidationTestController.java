package com.lanhu.lims.gateway.admin.validation.dict.example;

import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.core.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/********************************
 * @title DictValidationTestController
 * @package com.lanhu.lims.gateway.admin.validation.dict.example
 * @description 字典验证和数据回写功能测试控制器
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
@Slf4j
@RestController
@RequestMapping("/api/test/dict-validation")
@Api(tags = "字典验证和数据回写功能测试")
public class DictValidationTestController {
    
    /**
     * 测试用户状态字典验证和数据回写
     */
    @PostMapping("/user-status")
    @ApiOperation("测试用户状态字典验证")
    public PcsResult<UserStatusForm> testUserStatus(@RequestBody @Valid UserStatusForm form) {
        log.info("接收到用户状态表单: {}", form);
        
        // 此时form.statusName和form.statusNameEn已经被自动填充
        log.info("处理后的用户状态表单: {}", form);
        
        return Result.ok(form);
    }
    
    /**
     * 测试设备类型多值字典验证和数据回写
     */
    @PostMapping("/equipment-type")
    @ApiOperation("测试设备类型字典验证")
    public PcsResult<EquipmentTypeForm> testEquipmentType(@RequestBody @Valid EquipmentTypeForm form) {
        log.info("接收到设备类型表单: {}", form);
        
        // 此时字典相关字段已经被自动填充
        log.info("处理后的设备类型表单: {}", form);
        
        return Result.ok(form);
    }
}

/**
 * 测试步骤：
 * 
 * 1. 启动应用
 * 2. 访问Swagger文档：http://localhost:8080/swagger-ui.html
 * 3. 找到"字典验证和数据回写功能测试"分组
 * 
 * 4. 测试用户状态验证：
 *    POST /api/test/dict-validation/user-status
 *    请求体：
 *    {
 *      "userId": 1,
 *      "status": "1",
 *      "remark": "测试用户状态"
 *    }
 *    
 *    预期响应（假设字典中status=1对应"启用"）：
 *    {
 *      "code": 200,
 *      "data": {
 *        "userId": 1,
 *        "status": "1",
 *        "statusName": "启用",
 *        "statusNameEn": "Enable",
 *        "remark": "测试用户状态"
 *      }
 *    }
 * 
 * 5. 测试设备类型验证：
 *    POST /api/test/dict-validation/equipment-type
 *    请求体：
 *    {
 *      "equipmentCode": "EQ001",
 *      "equipmentName": "高效液相色谱仪",
 *      "usage": "0,1",
 *      "status": "1"
 *    }
 *    
 *    预期响应：
 *    {
 *      "code": 200,
 *      "data": {
 *        "equipmentCode": "EQ001",
 *        "equipmentName": "高效液相色谱仪",
 *        "usage": "0,1",
 *        "usageNames": "检测,分析",
 *        "status": "1",
 *        "statusName": "正常",
 *        "statusNameEn": "Normal"
 *      }
 *    }
 * 
 * 6. 测试验证失败：
 *    POST /api/test/dict-validation/user-status
 *    请求体：
 *    {
 *      "userId": 1,
 *      "status": "999",  // 无效状态值
 *      "remark": "测试"
 *    }
 *    
 *    预期响应：
 *    {
 *      "code": 400,
 *      "message": "用户状态值无效"
 *    }
 */
