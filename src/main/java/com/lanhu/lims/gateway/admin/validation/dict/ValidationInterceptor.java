package com.lanhu.lims.gateway.admin.validation.dict;

import cn.hutool.core.util.ArrayUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/********************************
 * @title ValidationInterceptor
 * @package com.lanhu.lims.gateway.admin.validation.dict
 * @description 验证拦截器，用于在验证前设置上下文
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
@Slf4j
@Aspect
@Component
@Order(0)
public class ValidationInterceptor {

    /**
     * 拦截Controller方法，在验证前设置上下文
     */
    @Around("execution(* com.lanhu.lims.gateway.admin.controller..*.*(..))")
    public Object aroundController(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();

        try {
            // 为每个参数设置验证上下文
            if (ArrayUtil.isNotEmpty(args)) {
                for (Object arg : args) {
                    if (arg != null && hasValidationAnnotation(arg)) {
                        ValidationContextHolder.setCurrentTarget(arg);
                        break; // 只设置第一个包含验证注解的参数
                    }
                }
            }

            return joinPoint.proceed();

        } finally {
            // 清除上下文
            ValidationContextHolder.clear();
        }
    }

    /**
     * 检查对象是否包含验证注解
     */
    private boolean hasValidationAnnotation(Object obj) {
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.isAnnotationPresent(DictDataValidation.class)) {
                return true;
            }
        }

        return false;
    }
}
