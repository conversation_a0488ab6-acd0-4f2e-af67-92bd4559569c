package com.lanhu.lims.gateway.admin.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 后台用户token表
 */
@ApiModel(value = "后台用户token表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "t_admin_user_token")
public class AdminUserToken {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * token
     */
    @TableField(value = "token")
    @ApiModelProperty(value = "token")
    private String token;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     * 是否有效，0：有效，1：无效
     */
    @TableField(value = "is_effect")
    @ApiModelProperty(value = "是否有效，0：有效，1：无效")
    private Integer isEffect;

    /**
     * 浏览器版本
     */
    @TableField(value = "os_version")
    @ApiModelProperty(value = "浏览器版本")
    private String osVersion;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户ID")
    private Long userId;
}