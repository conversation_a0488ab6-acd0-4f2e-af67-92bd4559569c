# 字典数据验证和数据回写功能（枚举版）

## 功能概述

`@DictDataValidation` 注解提供了类似于 `@NotNull`、`@NotBlank` 的Bean Validation参数校验功能，但具有以下特殊能力：
1. 使用`DictDataTypeEnum`枚举进行类型安全的字典验证
2. 支持单值和多值验证（通过分隔符）
3. 将查询到的字典数据自动回写到实体类指定字段中
4. 完全符合JSR-303 Bean Validation规范

## 核心组件

- `@DictDataValidation` - 字典验证注解（支持枚举类型）
- `DictDataValidator` - 验证器（实现ConstraintValidator接口）
- `ValidationContextHolder` - 验证上下文持有者
- `ValidationInterceptor` - 验证拦截器（AOP切面）

## 使用方式

### 1. 单值字典验证和回写（推荐使用枚举）

```java
@Data
public class UserStatusForm {
    @DictDataValidation(
        dictType = DictDataTypeEnum.USER_STATUS,  // 使用枚举，类型安全
        message = "用户状态值无效",
        enableWriteBack = true,
        writeBackDictData = "statusDictData"      // 推荐回写到DictData对象
    )
    private String status;
    
    @JsonIgnore
    private DictData statusDictData;  // 自动回写完整的字典对象
}
```

### 2. 多值字典验证和回写

```java
@Data
public class EquipmentForm {
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_USAGE,
        delimiter = ",",
        message = "设备用途值无效",
        enableWriteBack = true,
        writeBackDictDataList = "usageDictDataList"
    )
    private List<String> usage; // 如：["0", "1", "2"]
    
    @JsonIgnore
    private List<DictData> usageDictDataList; // 自动回写字典对象列表
}
```

### 3. 实际应用示例（EquipmentAddForm）

```java
@Data
public class EquipmentAddForm {
    /**
     * 仪器用途 - 多值验证，回写到List<DictData>
     */
    @NotEmpty(message = "仪器用途不能为空")
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_USAGE,
        message = "仪器用途值无效",
        enableWriteBack = true,
        writeBackDictDataList = "usageDictDataList"
    )
    private List<String> usage;
    
    /**
     * 仪器所属 - 单值验证，回写到DictData对象
     */
    @NotNull(message = "仪器所属不能为空")
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_OWNERSHIP_STATUS,
        message = "仪器所属值无效",
        enableWriteBack = true,
        writeBackDictData = "ownershipStatusDictData"
    )
    private Integer ownershipStatus;
    
    // 回写字段
    @JsonIgnore
    private List<DictData> usageDictDataList;
    
    @JsonIgnore
    private DictData ownershipStatusDictData;
}
```

## 注解参数说明

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| dictType | DictDataTypeEnum | 是 | - | 字典类型枚举值 |
| delimiter | String | 否 | "," | 多值分隔符 |
| allowEmpty | boolean | 否 | true | 是否允许空值 |
| message | String | 否 | "{com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.message}" | 验证失败错误消息（支持国际化） |
| writeBack | String[] | 否 | {} | 传统字段映射回写配置 |
| enableWriteBack | boolean | 否 | false | 是否启用数据回写 |
| writeBackDictData | String | 否 | "" | 回写到DictData对象的字段名 |
| writeBackDictDataList | String | 否 | "" | 回写到List<DictData>的字段名 |
| writeBackRequired | boolean | 否 | false | 回写失败是否抛异常 |

## 数据回写配置

支持三种回写方式：

### 1. 回写到DictData对象（推荐）
```java
@DictDataValidation(
    dictType = DictDataTypeEnum.USER_STATUS,
    enableWriteBack = true,
    writeBackDictData = "statusDictData"  // 回写到DictData对象字段
)
private String status;

@JsonIgnore
private DictData statusDictData;  // 会被自动填充
```

### 2. 回写到List<DictData>（推荐）
```java
@DictDataValidation(
    dictType = DictDataTypeEnum.EQUIPMENT_USAGE,
    enableWriteBack = true,
    writeBackDictDataList = "usageDictDataList"  // 回写到DictData列表字段
)
private List<String> usage;

@JsonIgnore
private List<DictData> usageDictDataList;  // 会被自动填充
```

### 3. 传统字段映射回写
```java
@DictDataValidation(
    dictType = DictDataTypeEnum.USER_STATUS,
    enableWriteBack = true,
    writeBack = {
        "dict_label:statusName",      // 字典标签 -> statusName字段
        "dict_label_en:statusNameEn"  // 英文标签 -> statusNameEn字段
    }
)
private String status;

private String statusName;    // 会被自动填充
private String statusNameEn;  // 会被自动填充
```

## 使用枚举的优势

1. **类型安全**：编译时检查，避免字符串拼写错误
2. **IDE支持**：自动补全和重构支持
3. **集中管理**：所有字典类型在`DictDataTypeEnum`中统一管理
4. **可维护性**：修改字典类型时IDE会提示所有使用位置

## 支持的输入类型

- `String` - 单个字符串值
- `List<String>` - 字符串列表
- `Integer` - 整数值
- `Long` - 长整数值
- 其他基本类型会自动转换为字符串进行验证

## 使用效果示例

前端传入：
```json
{
  "equipmentName": "高效液相色谱仪",
  "usage": ["0", "1"],
  "ownershipStatus": 0
}
```

后端接收到的数据（自动填充）：
```json
{
  "equipmentName": "高效液相色谱仪",
  "usage": ["0", "1"],
  "usageDictDataList": [
    {"id": 101, "dictValue": "0", "dictLabel": "检测"},
    {"id": 102, "dictValue": "1", "dictLabel": "分析"}
  ],
  "ownershipStatus": 0,
  "ownershipStatusDictData": {
    "id": 201, "dictValue": "0", "dictLabel": "自有"
  }
}
```

## 国际化支持

### 1. 配置国际化消息

在`messages_zh_CN.properties`中：
```properties
# 通用字典验证消息
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.message = 字典数据验证失败

# 特定字典类型的消息
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.usage.message = 仪器用途值无效
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.ownership.message = 仪器所属值无效
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.user.status.message = 用户状态值无效
```

在`messages_en_US.properties`中：
```properties
# 通用字典验证消息
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.message = Dictionary data validation failed

# 特定字典类型的消息
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.usage.message = Invalid equipment usage value
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.ownership.message = Invalid equipment ownership value
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.user.status.message = Invalid user status value
```

### 2. 使用国际化消息

```java
@DictDataValidation(
    dictType = DictDataTypeEnum.EQUIPMENT_USAGE,
    message = "{com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.usage.message}",
    enableWriteBack = true,
    writeBackDictDataList = "usageDictDataList"
)
private List<String> usage;
```

### 3. 与标准Bean Validation一致

```java
@NotEmpty(message = "{javax.validation.constraints.NotEmpty.message}")
@DictDataValidation(
    dictType = DictDataTypeEnum.EQUIPMENT_USAGE,
    message = "{com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.usage.message}"
)
private List<String> usage;
```

## 注意事项

1. **枚举使用**：推荐使用`DictDataTypeEnum`枚举而不是字符串
2. **国际化消息**：使用`{key}`格式支持国际化，与标准Bean Validation一致
3. **回写字段**：建议使用`@JsonIgnore`隐藏回写字段，避免前端传入
4. **性能考虑**：每个注解都会执行数据库查询，建议合理使用
5. **事务处理**：查询使用`@DS("slave_1")`只读数据源
6. **异常处理**：验证失败会抛出Bean Validation标准异常
