# 字典数据验证和数据回写功能

## 功能概述

`@DictDataValidation` 注解提供了类似于 `@NotNull`、`@NotBlank` 的Bean Validation参数校验功能，但具有以下特殊能力：
1. 根据字典类型验证字典值是否有效
2. 支持单值和多值验证（通过分隔符）
3. 将查询到的字典数据自动回写到实体类指定字段中
4. 完全符合JSR-303 Bean Validation规范

## 核心组件

- `@DictDataValidation` - 字典验证注解
- `DictDataValidator` - 验证器（实现ConstraintValidator接口）
- `ValidationContextHolder` - 验证上下文持有者
- `ValidationInterceptor` - 验证拦截器（AOP切面）

## 使用方式

### 1. 单值字典验证和回写

```java
@Data
public class UserStatusForm {
    @DictDataValidation(
        dictType = "user_status",
        message = "用户状态值无效",
        enableWriteBack = true,
        writeBack = {"dict_label:statusName", "dict_label_en:statusNameEn"}
    )
    private String status;
    
    private String statusName;    // 自动回写
    private String statusNameEn;  // 自动回写
}
```

### 2. 多值字典验证和回写

```java
@Data
public class EquipmentForm {
    @DictDataValidation(
        dictType = "equipment_usage",
        delimiter = ",",
        message = "设备用途值无效",
        enableWriteBack = true,
        writeBack = {"dict_label:usageNames"}
    )
    private String usage; // 如："0,1,2"
    
    private String usageNames; // 自动回写，如："检测,分析,其他"
}
```

## 注解参数说明

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| dictType | String | 是 | - | 字典类型枚举值 |
| delimiter | String | 否 | "," | 多值分隔符 |
| allowEmpty | boolean | 否 | true | 是否允许空值 |
| message | String | 否 | "字典数据验证失败" | 验证失败错误消息 |
| writeBack | String[] | 否 | {} | 数据回写配置 |
| enableWriteBack | boolean | 否 | false | 是否启用数据回写 |
| writeBackRequired | boolean | 否 | false | 回写失败是否抛异常 |

## 数据回写配置

`writeBack` 参数格式：`"查询结果字段名:实体字段名"`

支持的查询结果字段：
- `dict_label` - 字典标签
- `dict_label_en` - 字典标签（英文）
- `dict_value` - 字典值
- `dict_sort` - 字典排序
- `remark` - 备注

示例：
```java
writeBack = {
    "dict_label:statusName",      // 字典标签 -> statusName字段
    "dict_label_en:statusNameEn", // 英文标签 -> statusNameEn字段
    "remark:statusRemark"         // 备注 -> statusRemark字段
}
```

## 工作流程

1. **AOP拦截**：`ValidationInterceptor`拦截Controller方法
2. **上下文设置**：将包含验证注解的参数对象设置到ThreadLocal
3. **Bean Validation**：Spring框架调用`DictDataValidator.isValid()`方法
4. **字典查询**：根据`dictType`和字段值查询数据库
5. **验证判断**：检查所有值是否都在有效字典数据中
6. **数据回写**：如果启用回写，将查询结果写入指定字段
7. **上下文清理**：清除ThreadLocal中的对象引用

## 数据库查询逻辑

验证器使用以下SQL逻辑查询字典数据：

```sql
SELECT * FROM t_dict_data d
WHERE d.is_effect = 0 
  AND d.status = 1
  AND d.dict_value IN ('value1', 'value2', ...)
  AND EXISTS (
      SELECT 1 FROM t_dict_data parent 
      WHERE parent.id = d.parent_id 
        AND parent.dict_value = #{dictType}
        AND parent.is_effect = 0 
        AND parent.status = 1
  )
```

## 使用场景

### 1. 状态字段验证
```java
@DictDataValidation(dictType = "user_status", enableWriteBack = true, 
                   writeBack = {"dict_label:statusName"})
private String status;
```

### 2. 多选字段验证
```java
@DictDataValidation(dictType = "equipment_usage", delimiter = ",", 
                   enableWriteBack = true, writeBack = {"dict_label:usageNames"})
private String usage; // "0,1,2"
```

### 3. 枚举值验证
```java
@DictDataValidation(dictType = "material_type", enableWriteBack = true,
                   writeBack = {"dict_label:typeName", "dict_label_en:typeNameEn"})
private String materialType;
```

## 注意事项

1. **性能考虑**：每个注解都会执行数据库查询，建议合理使用
2. **事务处理**：查询使用`@DS("slave_1")`只读数据源
3. **ThreadLocal清理**：AOP切面会自动清理ThreadLocal，避免内存泄漏
4. **字段类型**：确保回写的字段类型兼容（通常为String类型）
5. **异常处理**：验证失败会抛出Bean Validation标准异常

## 扩展功能

### 1. 自定义分隔符
```java
@DictDataValidation(dictType = "tags", delimiter = ";") // 分号分隔
@DictDataValidation(dictType = "tags", delimiter = "|") // 竖线分隔
@DictDataValidation(dictType = "tags", delimiter = "/") // 斜杠分隔
```

### 2. 严格模式
```java
@DictDataValidation(dictType = "status", allowEmpty = false, 
                   writeBackRequired = true) // 不允许空值，回写必须成功
```

### 3. 仅验证不回写
```java
@DictDataValidation(dictType = "status", enableWriteBack = false) // 仅验证
```

## 与原有功能的兼容性

此功能完全兼容现有的Bean Validation体系，可以与其他验证注解组合使用：

```java
@NotBlank(message = "状态不能为空")
@DictDataValidation(dictType = "user_status", message = "状态值无效")
private String status;
```
