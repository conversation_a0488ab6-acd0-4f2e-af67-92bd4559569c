package com.lanhu.lims.gateway.admin.validation.dict.example;

import com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/********************************
 * @title UserStatusForm
 * @package com.lanhu.lims.gateway.admin.validation.dict.example
 * @description 用户状态表单示例 - 字典验证和数据回写
 * <AUTHOR>
 * @date 2025-06-19
 * @version 1.0.0
 *********************************/
@Data
@ApiModel(description = "用户状态表单示例")
public class UserStatusForm {
    
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 用户状态 - 使用字典验证和数据回写
     * 验证状态值是否在用户状态字典中存在，并自动回写状态名称
     */
    @ApiModelProperty(value = "用户状态", required = true)
    @NotBlank(message = "用户状态不能为空")
    @DictDataValidation(
        dictType = "user_status",
        message = "用户状态值无效",
        enableWriteBack = true,
        writeBack = {"dict_label:statusName", "dict_label_en:statusNameEn"},
        writeBackRequired = false
    )
    private String status;
    
    /**
     * 状态名称 - 会被自动回写
     */
    @ApiModelProperty(value = "状态名称")
    private String statusName;
    
    /**
     * 状态名称（英文） - 会被自动回写
     */
    @ApiModelProperty(value = "状态名称（英文）")
    private String statusNameEn;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}

/**
 * 使用示例：
 * 
 * 1. 在Controller中：
 * @PostMapping("/user/status/update")
 * public Result updateUserStatus(@RequestBody @Valid UserStatusForm form) {
 *     // 此时form.statusName和form.statusNameEn已经被自动填充
 *     return Result.ok(form);
 * }
 * 
 * 2. 前端传入：
 * {
 *   "userId": 123,
 *   "status": "1",
 *   "remark": "更新用户状态"
 * }
 * 
 * 3. 后端接收到的数据（假设字典中status=1对应"启用"）：
 * {
 *   "userId": 123,
 *   "status": "1",
 *   "statusName": "启用",        // 自动回写
 *   "statusNameEn": "Enable",   // 自动回写
 *   "remark": "更新用户状态"
 * }
 * 
 * 4. 如果传入无效的status值，会抛出验证异常：
 * {
 *   "userId": 123,
 *   "status": "999",  // 无效值
 *   "remark": "测试"
 * }
 * 响应：400 Bad Request - "用户状态值无效"
 */
