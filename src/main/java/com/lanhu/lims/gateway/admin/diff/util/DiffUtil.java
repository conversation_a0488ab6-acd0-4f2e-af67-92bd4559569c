package com.lanhu.lims.gateway.admin.diff.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.idev.excel.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lanhu.lims.gateway.admin.core.ProjectConstant;
import com.lanhu.lims.gateway.admin.diff.annotation.JaversProperty;
import com.lanhu.lims.gateway.admin.diff.enums.CompareTypeEnum;
import com.lanhu.lims.gateway.admin.diff.vo.*;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.service.DictDataService;
import com.lanhu.lims.gateway.admin.utils.SpringUtils;
import com.lanhu.lims.gateway.admin.utils.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.ValueChange;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/6/12 11:06
 */
@Service
@Slf4j
public class DiffUtil {


    private static  final String NONE = "无";


    private static  final String UPDATE_TITLE = "编辑内容";


    private static  final String FILE_ATTACHMENT_TITLE = "文件附件";


    private static  final String FILE_ATTACHMENT_MIDDLE = "文件";

    //忽略比较字段
    private static final  List IGNORE_DIFF_PROPERTY = Lists.newArrayList("id","status","isEffect","createBy","createName","createTime","updateBy","updateName","updateTime","closeTime","completeBy","completeName","closeBy",
            "closeName","auditTime","auditBy","auditRemark","auditName","auditStatus","flowInstanceId","customerId");

    /**
     * @description: 属性名称翻译注解解析
     * @param: [clazz]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     * @author: liuyi
     * @date: 22:08 2025/6/11
     */
    private static Map<String, String> propertyNameTranslation(Class<?> clazz) {
        Map<String, String> translations = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(ApiModelProperty.class)) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                translations.put(field.getName(), annotation.value());
            }
        }
        return translations;
    }


    /**
    * @description: 属性值翻译主机解析
    * @param: [clazz]
    * @return: java.util.Map<java.lang.String,com.lanhu.lims.gateway.admin.diff.vo.PropertyValueTranslation>
    * @author: liuyi
    * @date: 11:20 2025/6/12 
    */
    private static  Map<String, PropertyValueTranslation> propertyValueTranslation(Class<?> clazz) {
        Map<String, PropertyValueTranslation> translations = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(JaversProperty.class)) {
                JaversProperty annotation = field.getAnnotation(JaversProperty.class);
                translations.put(field.getName(), new PropertyValueTranslation(annotation.dictId(),annotation.split(), annotation.title(),annotation.name(),annotation.pattern()));
            }
        }
        return translations;
    }



    /**
    * @description: 文件附件比对
    * @param: [fileAttachmentChangeResult]
    * @return: com.lanhu.lims.gateway.admin.diff.vo.ChangeResult
    * @author: liuyi
    * @date: 18:12 2025/6/13 
    */
    public static ChangeResult compare(FileAttachmentChangeResult fileAttachmentChangeResult){
        ChangeResult changeResult = new  ChangeResult();

        //文件附件变动
        List<FileAttachmentChangeResult> fileAttachmentChangeResultList = Lists.newArrayList();

        //设置文件附件内容变动
        if(fileAttachmentChangeResult != null){
            fileAttachmentChangeResultList = fileAttachmentChangeResultConvert(fileAttachmentChangeResult);
        }

        changeResult.setFileAttachmentChangeResultList(fileAttachmentChangeResultList);

        return changeResult;

    }

    /**
    * @description: 比对工具类，只比对编辑内容变动
    * @param: [oldObj, newObj]
    * @return: com.lanhu.lims.gateway.admin.diff.vo.ChangeResult
    * @author: liuyi
    * @date: 23:24 2025/6/12
    */
    public static ChangeResult compare(Object oldObj, Object newObj) {
        return compare(oldObj,newObj,null);
    }

        /**
         * @description:  比对工具类，只比对编辑内容变动，文件附件比对
         * @param: [oldObj, newObj]
         * @return: java.lang.String
         * @author: liuyi
         * @date: 22:10 2025/6/11
         */
    public static ChangeResult compare(Object oldObj, Object newObj, FileAttachmentChangeResult fileAttachmentChangeResult){

        ChangeResult changeResult = new  ChangeResult();


        UpdateChangeResult updateChangeResult = new UpdateChangeResult();

        //修改变动项目
        List<UpdateChangeItem> updateChangeItemList = Lists.newArrayList();


        //文件附件变动
        List<FileAttachmentChangeResult> fileAttachmentChangeResultList = Lists.newArrayList();


        //新增删除变动项
        List<InsertOrDeleteChangeResult> insertOrDeleteChangeResultList = Lists.newArrayList();


        DictDataService dictDataService = SpringUtils.getBean(DictDataService.class);

        //所有字段数据
        List<DictData> dictDataList = dictDataService.list();


        List<UpdateChangeResult> changeResultList = Lists.newArrayList();

        List<String> diffList = Lists.newArrayList();

        // 初始化 JaVers
        Javers javers = JaversBuilder.javers()
                .build();


        Diff diff =  javers.compare(oldObj,newObj);

        // 获取属性翻译映射
        Map<String, String> propertyNameTranslationMap = propertyNameTranslation(oldObj.getClass());

        //获取属性值翻译主机解析
        Map<String, PropertyValueTranslation> propertyValueTranslationMap =  propertyValueTranslation(oldObj.getClass());


        // 提取 ValueChange 并格式化输出
        diff.getChangesByType(ValueChange.class).forEach(change -> {

            String propertyName = change.getPropertyName();

            //如果字段忽略，则不输出比对结果
            if(IGNORE_DIFF_PROPERTY.contains(propertyName)){
                return;
            }

            //显示名称，如果不存在，则显示属性名
            String displayName = propertyNameTranslationMap.getOrDefault(propertyName, propertyName);

            PropertyValueTranslation propertyValueTranslation = propertyValueTranslationMap.get(propertyName);


            //变动属性名称优先级 注解propertyValueTranslation name ----> ApiModelProperty name
            if(propertyValueTranslation != null && StringUtil.isNotBlank(propertyValueTranslation.getName())){
                displayName = propertyValueTranslation.getName();
            }


            String oldValue = String.valueOf(change.getLeft());  // 旧值

            String newValue = String.valueOf(change.getRight()); // 新值

            //日期格式转换

            if(propertyValueTranslation != null && StringUtil.isNotBlank(propertyValueTranslation.getPattern())){
                if(change.getLeft() != null){
                   oldValue = DateUtil.format((Date) change.getLeft(),propertyValueTranslation.getPattern());
                }

                if(change.getRight() != null){
                    newValue = DateUtil.format((Date) change.getRight(),propertyValueTranslation.getPattern());
                }
            }


            //则说明是修改变动
            if(propertyValueTranslation == null){


                if(StringUtil.isBlank(oldValue) || oldValue.equalsIgnoreCase("null")){
                    oldValue = NONE;
                }

                if(StringUtil.isBlank(newValue) || newValue.equalsIgnoreCase("null")){
                    newValue = NONE;
                }

                //原数据和编辑后的数据不一致
                if(!oldValue.equalsIgnoreCase(newValue)){
                    updateChangeItemList.add(new UpdateChangeItem(displayName,oldValue,newValue));
                }

             //则说明变动需要翻译
            }else {

                //如果属性值不需要拆分
                if(StringUtils.isBlank(propertyValueTranslation.getSplit())){


                    //则属性值不需要翻译
                    if(propertyValueTranslation.getDicId() == null || propertyValueTranslation.getDicId() <=0 ){


                        if(StringUtil.isBlank(oldValue) || oldValue.equalsIgnoreCase("null")){
                            oldValue = NONE;
                        }

                        if(StringUtil.isBlank(newValue) || newValue.equalsIgnoreCase("null")){
                            newValue = NONE;
                        }

                        //原数据和编辑后的数据不一致
                        if(!oldValue.equalsIgnoreCase(newValue)){
                            updateChangeItemList.add(new UpdateChangeItem(displayName,oldValue,newValue));
                        }
                        //则属性值需要翻译
                    }else {

                        if(StringUtil.isBlank(oldValue) || oldValue.equalsIgnoreCase("null")){
                            oldValue = NONE;
                        }else {
                            oldValue = valueTranslation(oldValue,propertyValueTranslation,dictDataList);

                            if(StringUtil.isBlank(oldValue)){
                                oldValue = NONE;
                            }
                        }

                        if(StringUtil.isBlank(newValue) || newValue.equalsIgnoreCase("null")){
                            newValue = NONE;
                        }else {
                            newValue = valueTranslation(newValue,propertyValueTranslation,dictDataList);

                            if(StringUtil.isBlank(newValue)){
                                newValue = NONE;
                            }

                        }

                        //原数据和编辑后的数据不一致
                        if(!oldValue.equalsIgnoreCase(newValue)){
                            updateChangeItemList.add(new UpdateChangeItem(displayName,oldValue,newValue));
                        }

                    }

                //属性值需要拆分
                }else {

                    InsertOrDeleteChangeResult insertOrDeleteChangeResult = new InsertOrDeleteChangeResult();
                    insertOrDeleteChangeResult.setTitle(propertyValueTranslation.getTitle());

                    List<InsertOrDeleteChangeItem> insertOrDeleteChangeItemList = Lists.newArrayList();

                    //则属性值不需要翻译
                    if(propertyValueTranslation.getDicId() == null || propertyValueTranslation.getDicId() <=0 ){

                        insertOrDeleteChangeItemList = insertOrDeleteChangeItemConvert(oldValue,newValue,propertyValueTranslation.getSplit(),displayName);

                    //属性值需要翻译
                    }else {

                        //原始数据转换
                        oldValue = valueTranslation(oldValue,propertyValueTranslation,dictDataList);

                        //修改后数据转换
                        newValue = valueTranslation(newValue,propertyValueTranslation,dictDataList);

                        insertOrDeleteChangeItemList = insertOrDeleteChangeItemConvert(oldValue,newValue,propertyValueTranslation.getSplit(),displayName);



                    }

                    insertOrDeleteChangeResult.setItemList(insertOrDeleteChangeItemList);



                    if(CollectionUtil.isNotEmpty(insertOrDeleteChangeItemList)){
                        insertOrDeleteChangeResultList.add(insertOrDeleteChangeResult);
                    }


                }

            }



        });

        //设置编辑内容变动
        updateChangeResult.setTitle(UPDATE_TITLE);
        updateChangeResult.setItemList(updateChangeItemList);
        changeResult.setUpdateChangeResult(updateChangeResult);

        //设置新增删除内容变动
        changeResult.setInsertOrDeleteChangeResultList(insertOrDeleteChangeResultList);

        //设置文件附件内容变动
        if(fileAttachmentChangeResult != null){
            fileAttachmentChangeResultList = fileAttachmentChangeResultConvert(fileAttachmentChangeResult);
        }

        changeResult.setFileAttachmentChangeResultList(fileAttachmentChangeResultList);

        return changeResult;
    }



    /**
    * @description: 新增删除数据变动转化
    * @param: [oldValue, newValue]
    * @return: java.util.List<com.lanhu.lims.gateway.admin.diff.vo.InsertOrDeleteChangeItem>
    * @author: liuyi
    * @date: 21:44 2025/6/12 
    */
    private static  List<InsertOrDeleteChangeItem> insertOrDeleteChangeItemConvert(String oldValue,String newValue,String split,String middle){

        List<InsertOrDeleteChangeItem> itemList = Lists.newArrayList();

        //原数据列表
        List<String> oldList = Lists.newArrayList();
        //修改后数据列表
        List<String> newList = Lists.newArrayList();
        //新增列表
        List<String> insertList = Lists.newArrayList();
        //删除列表
        List<String> delList = Lists.newArrayList();


        if(StringUtil.isNotBlank(oldValue)){
            oldList = ListUtil.toList(oldValue.split(split));
        }

        if(StringUtil.isNotBlank(newValue)){
            newList =  ListUtil.toList(newValue.split(split));
        }

        //新增---修改后列表存在的，原始列表中不存在的
        insertList = CollectionUtil.subtractToList(newList,oldList);
        
        //如果新增存在
        if(CollectionUtil.isNotEmpty(insertList)){

            for (String value : insertList) {
                InsertOrDeleteChangeItem insertOrDeleteChangeItem = new InsertOrDeleteChangeItem(CompareTypeEnum.ADD.getMsg(),middle,value);
                itemList.add(insertOrDeleteChangeItem);
            }
        }


        //删除---原始列表中存在，修改后不存在
        delList = CollectionUtil.subtractToList(oldList,newList);

        //如果删除存在
        if(CollectionUtil.isNotEmpty(delList)){

            for (String value : delList) {
                InsertOrDeleteChangeItem insertOrDeleteChangeItem = new InsertOrDeleteChangeItem(CompareTypeEnum.DELETE.getMsg(),middle,value);
                itemList.add(insertOrDeleteChangeItem);
            }
        }



        return itemList;
    }

    
    /**
    * @description: 文件附件转换
    * @param: [fileAttachmentChangeResult]
    * @return: java.lang.String
    * @author: liuyi
    * @date: 10:53 2025/6/13 
    */
    private static  List<FileAttachmentChangeResult> fileAttachmentChangeResultConvert( FileAttachmentChangeResult fileAttachmentChangeResult){
        //文件附件变动
        List<FileAttachmentChangeResult> fileAttachmentChangeResultList = Lists.newArrayList();


        if(fileAttachmentChangeResult != null){

            fileAttachmentChangeResult.setTitle(FILE_ATTACHMENT_TITLE);

            if(CollectionUtil.isNotEmpty( fileAttachmentChangeResult.getItemList())){
                for (FileAttachmentChangeItem fileAttachmentChangeItem : fileAttachmentChangeResult.getItemList()) {
                    fileAttachmentChangeItem.setMiddle(FILE_ATTACHMENT_MIDDLE);
                    //如果文件上传ID存在
                    if(StringUtil.isNotBlank(fileAttachmentChangeItem.getAttachmentId())){
                        fileAttachmentChangeItem.setCompareType(CompareTypeEnum.DELETE.getMsg());
                    }else {
                        fileAttachmentChangeItem.setCompareType(CompareTypeEnum.ADD.getMsg());

                    }
                }
            }

            fileAttachmentChangeResultList.add(fileAttachmentChangeResult);
        }


        return  fileAttachmentChangeResultList;

    }

    /**
    * @description: 根据字典数据进行翻译
    * @param: [value, propertyValueTranslation]
    * @return: java.lang.String
    * @author: liuyi
    * @date: 09:00 2025/6/13 
    */
    private static  String valueTranslation(String value,PropertyValueTranslation propertyValueTranslation,List<DictData> dictDataList){


        //翻译后值
        String valueTranslation = ProjectConstant.EMPTY;


        if(StringUtil.isBlank(value)){
            return valueTranslation;
        }

        //加载字典数据
        Map<String,DictData> dictDataMap = Maps.newHashMap();

        //原始数据列表
        List<String> valueList  = Lists.newArrayList();

        //翻译后列表
        List<String> valueTranslationList = Lists.newArrayList();

        if(propertyValueTranslation != null && StringUtil.isNotBlank(propertyValueTranslation.getSplit())){
            valueList =   CollectionUtil.newArrayList(value.split(propertyValueTranslation.getSplit()));
        }else {
            valueList.add(value);
        }


        //需要翻译
        if(propertyValueTranslation != null && propertyValueTranslation.getDicId() != null && propertyValueTranslation.getDicId() > 0){


            dictDataMap =  dictDataList.stream().filter(data-> data.getParentId().intValue() == propertyValueTranslation.getDicId() ).collect(Collectors.toMap(data -> data.getDictValue(), data -> data));


            if(CollectionUtil.isNotEmpty(valueList)){

                for (String v : valueList) {
                    if(dictDataMap.get(v) != null && StringUtil.isNotBlank(dictDataMap.get(v).getDictLabel())){
                        valueTranslationList.add(dictDataMap.get(v).getDictLabel());
                    }
                }
            }

        }else {

            if(CollectionUtil.isNotEmpty(valueList)){
                valueTranslationList.addAll(valueList);
            }

        }


        if(CollectionUtil.isNotEmpty(valueTranslationList)){

            if(StringUtil.isNotBlank(propertyValueTranslation.getSplit())  && valueTranslationList.size() > 1){
                valueTranslation = String.join(propertyValueTranslation.getSplit(),valueTranslationList);
            }else {
                valueTranslation = valueTranslationList.get(0);
            }

        }



        return valueTranslation;


    }


}
