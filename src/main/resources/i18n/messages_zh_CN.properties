SUCCESS=\u6210\u529F
ERROR_OPERATE=\u64CD\u4F5C\u5931\u8D25



#@NotBlank
javax.validation.constraints.NotBlank.message = \u5B57\u6BB5 {0} \u4E3A\u7A7A
#@NotNull
javax.validation.constraints.NotNull.message = \u5B57\u6BB5 {0} \u4E3A\u7A7A
#@Length
org.hibernate.validator.constraints.Length.message = \u5B57\u6BB5 {0} \u957F\u5EA6\u4E0D\u5BF9

javax.validation.constraints.Min.message = \u5B57\u6BB5 {0} \u503C\u9519\u8BEF
javax.validation.constraints.password.regexp.message=\u5BC6\u7801\u81F3\u5C116\u4F4D\u5B57\u7B26\uFF0C\u652F\u6301\u6570\u5B57\u3001\u5B57\u6BCD\u548C\u9664\u7A7A\u683C\u5916\u7684\u7279\u6B8A\u5B57\u7B26\uFF0C\u4E14\u5FC5\u987B\u540C\u65F6\u5305\u542B\u6570\u5B57\u548C\u5927\u5C0F\u5199\u5B57\u6BCD

#@DictDataValidation
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.message = \u5B57\u5178\u6570\u636E\u9A8C\u8BC1\u5931\u8D25
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.usage.message = \u4EEA\u5668\u7528\u9014\u503C\u65E0\u6548
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.equipment.ownership.message = \u4EEA\u5668\u6240\u5C5E\u503C\u65E0\u6548
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.user.status.message = \u7528\u6237\u72B6\u6001\u503C\u65E0\u6548
com.lanhu.lims.gateway.admin.validation.dict.DictDataValidation.user.gender.message = \u7528\u6237\u6027\u522B\u503C\u65E0\u6548
NOT_FOUND_HANDLER=\u8BF7\u6C42\u8D44\u6E90\u5730\u5740\u4E0D\u5B58\u5728
METHOD_NOT_SUPPORT=\u8BF7\u6C42\u65B9\u6CD5\u9519\u8BEF
MEDIA_TYPE_NOT_SUPPORT=\u8BF7\u6C42\u7C7B\u578B\u4E0D\u5408\u6CD5
PARAM_LOSE=\u53C2\u6570\u7F3A\u5931
PARAM_TYPE_NOT_SUPPORT=\u53C2\u6570\u7C7B\u578B\u4E0D\u5339\u914D

FILE_UPLOAD_ERROR=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
UPLOAD_FILE_CONFIG_ISNULL_ERROR=\u6587\u4EF6\u7C7B\u578B\u6CA1\u6709\u914D\u7F6E
UPLOAD_OVER_SIZE=\u4E0A\u4F20\u7684\u6587\u4EF6\u8D85\u8FC7\u89C4\u5B9A\u6587\u4EF6\u7684\u4E2A\u6570
UPLOAD_FILE_SUF_ERROR=\u4E0A\u4F20\u7684\u6587\u4EF6\u7C7B\u578B\u4E0D\u5BF9
UPLOAD_OVER_LIMIT=\u4E0A\u4F20\u7684\u6587\u4EF6\u8D85\u8FC7\u89C4\u5B9A\u6587\u4EF6\u7684\u5927\u5C0F
UPLOAD_NEED_FILE=\u8BF7\u9009\u62E9\u4E0A\u4F20\u6587\u4EF6
FILE_NOT_EXIST=\u6587\u4EF6\u4E0D\u5B58\u5728

DATA_WAS_AUDITING_CANT_EDIT_OR_DELETE = \u6B64\u6570\u636E\u6B63\u5728\u5BA1\u6838\u4E2D\uFF0C\u4E0D\u5141\u8BB8\u64CD\u4F5C

NOT_TOKEN=\u5F53\u524D\u4F1A\u8BDD\u672A\u767B\u5F55
INVALID_TOKEN=token \u65E0\u6548
TOKEN_TIMEOUT=token \u5DF2\u8FC7\u671F
BE_REPLACED=token \u5DF2\u88AB\u9876\u4E0B\u7EBF
KICK_OUT=token \u5DF2\u88AB\u8E22\u4E0B\u7EBF
TOKEN_FREEZE=token \u5DF2\u88AB\u51BB\u7ED3
NO_PREFIX=\u672A\u6309\u7167\u6307\u5B9A\u524D\u7F00\u63D0\u4EA4 token

CAPTCHA_NOT_EXIST=\u9A8C\u8BC1\u7801\u65E0\u6548


NO_PERMISSION=\u6CA1\u6709\u6743\u9650\u64CD\u4F5C
NO_ROLE=\u6CA1\u6709\u89D2\u8272\u64CD\u4F5C


PLA_USER_NOT_EXISTS=\u8D26\u53F7\u4E0D\u5B58\u5728
PLA_USER_CAN_NOT_LOGIN=\u7528\u6237\u4E0D\u5141\u8BB8\u767B\u5F55
PLA_USER_PWD_ERROR=\u5BC6\u7801\u5B89\u5168\u6027\u592A\u4F4E\uFF0C\u8BF7\u8BBE\u7F6E6-12\u4F4D\u6570\u5B57/\u5B57\u6BCD\u7EC4\u5408\u7684\u5BC6\u7801
PLA_USER_PWD_NOTSAME_ERROR=\u539F\u5BC6\u7801\u8F93\u5165\u9519\u8BEF
PLA_USER_EMAIL_NOT_EXISTS=\u90AE\u7BB1\u4E0D\u5B58\u5728
PLA_USER_CAPTCHA_SEND_FREQUENT=\u9A8C\u8BC1\u7801\u53D1\u9001\u592A\u9891\u7E41
PLA_USER_CAPTCHA_NOT_EXIST=\u9A8C\u8BC1\u7801\u4E0D\u5B58\u5728
PLA_USER_LOGIN_PASSWORD_ERROR=\u5BC6\u7801\u9519\u8BEF
PLA_USER_EMAIL_BIND_EXISTS=\u90AE\u7BB1\u5DF2\u7ECF\u88AB\u7ED1\u5B9A
PLA_USER_MOBILE_BIND_EXISTS=\u624B\u673A\u53F7\u7801\u5DF2\u7ECF\u88AB\u7ED1\u5B9A
PLA_USER_NAME_BIND_EXISTS=\u7528\u6237\u540D\u79F0\u5B58\u5728
PLA_USER_EMPLOYEE_ID_EXISTS=\u8BE5\u5458\u5DE5\u5DE5\u53F7\u5B58\u5728



PLA_ROLE_NOT_EXISTS=\u6743\u9650\u4E0D\u5B58\u5728
PLA_ROLE_NOT_ADMIN=\u975E\u8FD0\u8425\u8D26\u53F7\u6743\u9650
SEND_TEMPLATE_NOT_EXIST=\u53D1\u9001\u6A21\u677F\u4E0D\u5B58\u5728
ROLE_NAME_EXISTS=\u89D2\u8272\u540D\u79F0\u5B58\u5728

DEPT_HAS_CHILDREN = \u90E8\u95E8\u5B58\u5728\u5B50\u90E8\u95E8\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
DEPT_NOT_EXISTS = \u90E8\u95E8\u4E0D\u5B58\u5728


PARENT_CATEGORY_NOT_EXISTS = \u7236\u7C7B\u522B\u4E0D\u5B58\u5728
CATEGORY_NOT_EXISTS = \u7C7B\u522B\u4E0D\u5B58\u5728
CATEGORY_HAS_CHILDREN = \u7C7B\u522B\u5B58\u5728\u5B50\u7C7B\u522B\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664

PARENT_TYPE_NOT_EXISTS = \u7236\u7C7B\u578B\u4E0D\u5B58\u5728
TYPE_HAS_CHILDREN = \u7C7B\u578B\u5B58\u5728\u5B50\u7C7B\u578B\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664

BUSINESS_NOT_EXISTS =  \u5F53\u524D\u4E1A\u52A1\u4E0D\u5B58\u5728
JSON_CONVERT_EXCEPTION = JSON \u8F6C\u6362\u5F02\u5E38

PASS_TO_PREVIOUS_NODE = \u901A\u8FC7\u7684\u8282\u70B9\u5728\u5F53\u524D\u8282\u70B9\u4E4B\u524D\uFF0C\u4E0D\u5141\u8BB8\u64CD\u4F5C
REJECT_TO_SUFFIX_NODE = \u9A73\u56DE\u7684\u8282\u70B9\u5728\u5F53\u524D\u8282\u70B9\u4E4B\u540E\uFF0C\u4E0D\u5141\u8BB8\u64CD\u4F5C

FLOW_EXCEPTION = \u6D41\u7A0B\u5F02\u5E38
DictDataNotExist = \u5B57\u5178\u6570\u636E\u4E0D\u5B58\u5728

MENU_HAS_CHILD = \u5F53\u524D\u83DC\u5355\u5B58\u5728\u5B50\u83DC\u5355\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
MENU_NOT_EXIST = \u5F53\u524D\u83DC\u5355\u4E0D\u5B58\u5728



SYSTEM_FILE_TYPE_NOT_EXIST = \u6587\u4EF6\u7C7B\u578B\u4E0D\u5B58\u5728
SYSTEM_FILE_PARENT_TYPE_NOT_EXIST = \u6587\u4EF6\u7C7B\u578B\u7236\u7C7B\u578B\u4E0D\u5B58\u5728
SYSTEM_FILE_TYPE_NAME_EXISTS = \u6587\u4EF6\u7C7B\u578B\u540D\u79F0\u5B58\u5728
SYSTEM_FILE_TYPE_HAS_CHILDREN = \u6587\u4EF6\u7C7B\u578B\u5B58\u5728\u5B50\u7C7B\u578B\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
SYSTEM_FILE_TYPE_HAS_FILE = \u6587\u4EF6\u7C7B\u578B\u5B58\u5728\u6587\u4EF6\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
SYSTEM_FILE_NOT_EXIST = \u6587\u4EF6\u4E0D\u5B58\u5728

YEAR_CANT_BE_GREATER_THAN_CURRENT_YEAR = \u5E74\u4EFD\u4E0D\u80FD\u5927\u4E8E\u5F53\u524D\u5E74


ENTRUST_ORDER_NOT_EXIST = \u59D4\u6258\u5355\u4E0D\u5B58\u5728
ENTRUST_ORDER_STATUS_ERROR = \u59D4\u6258\u5355\u72B6\u6001\u9519\u8BEF
ENTRUST_ORDER_CUSTOMER_INFO_INCOMPLETE = \u59D4\u6258\u5355\u5BA2\u6237\u4FE1\u606F\u4E0D\u5B8C\u6574
ENTRUST_ORDER_SAMPLE_INFO_INCOMPLETE = \u59D4\u6258\u5355\u6837\u672C\u4FE1\u606F\u4E0D\u5B8C\u6574
ENTRUST_ORDER_ENTRUST_INFO_INCOMPLETE = \u59D4\u6258\u5355\u59D4\u6258\u4FE1\u606F\u4E0D\u5B8C\u6574
ENTRUST_ORDER_REPORT_INFO_INCOMPLETE = \u59D4\u6258\u5355\u62A5\u544A\u4FE1\u606F\u4E0D\u5B8C\u6574
ENTRUST_ORDER_INVOICE_INFO_INCOMPLETE = \u59D4\u6258\u5355\u53D1\u7968\u4FE1\u606F\u4E0D\u5B8C\u6574

STORE_POSITION_NOT_EXIST = \u5B58\u50A8\u4F4D\u7F6E\u4E0D\u5B58\u5728
STORE_POSITION_PARENT_NOT_EXIST = \u7236\u5B58\u50A8\u4F4D\u7F6E\u4E0D\u5B58\u5728
STORE_POSITION_NAME_EXISTS = \u5B58\u50A8\u4F4D\u7F6E\u540D\u79F0\u5B58\u5728
STORE_POSITION_HAS_CHILDREN = \u5B58\u50A8\u4F4D\u7F6E\u5B58\u5728\u5B50\u4F4D\u7F6E\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664

DICT_DATA_CATEGORY_NOT_EXIST = \u5B57\u5178\u5206\u7C7B\u4E0D\u5B58\u5728
DICT_DATA_NOT_EXIST = \u5B57\u5178\u6570\u636E\u4E0D\u5B58\u5728
DICT_DATA_PARENT_NOT_EXIST = \u5B57\u5178\u6570\u636E\u7236\u7C7B\u4E0D\u5B58\u5728
DICT_DATA_WAS_EXISTS = \u5B57\u5178\u6570\u636E\u5DF2\u7ECF\u5B58\u5728
DICT_DATA_HAS_CHILDREN = \u5B57\u5178\u6570\u636E\u5B58\u5728\u5B50\u7C7B\u578B\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
DICT_DATA_IS_NOT_VISIBLE = \u5B57\u5178\u6570\u636E\u4E0D\u53EF\u89C6\u5B8C

FILE_RECORD_NOT_EXIST = \u6587\u4EF6\u8BB0\u5F55\u4E0D\u5B58\u5728

DETECTION_METHOD_NOT_EXIST = \u68C0\u6D4B\u65B9\u6CD5\u4E0D\u5B58\u5728
DETECTION_METHOD_BIND_PROJECT = \u68C0\u6D4B\u65B9\u6CD5\u5DF2\u5173\u8054\u68C0\u6D4B\u9879\u76EE\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664

DETECTION_PROJECT_NOT_EXIST = \u68C0\u6D4B\u9879\u76EE\u4E0D\u5B58\u5728
DETECTION_PROJECT_BIND_ENTRUST_ORDER = \u68C0\u6D4B\u9879\u76EE\u5DF2\u5173\u8054\u59D4\u6258\u5355\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664

EXECUTION_STANDARD_NOT_EXIST = \u6267\u884C\u6807\u51C6\u4E0D\u5B58\u5728

TEMPLATE_FILE_NOT_EXIST = \u6A21\u677F\u6587\u4EF6\u4E0D\u5B58\u5728

SAMPLE_PLATE_NUM_NOT_CONSISTENT = \u6837\u672C\u677F\u53F7\u4E0D\u4E00\u81F4

EQUIPMENT_NOT_EXIST = \u8BBE\u5907\u4E0D\u5B58\u5728

REAGENT_NOT_EXIST = \u8BD5\u5242\u4E0D\u5B58\u5728
REAGENT_HAS_SPECIFICATIONS = \u8BD5\u5242\u5B58\u5728\u89C4\u683C\u6570\u636E\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
REAGENT_SPECIFICATION_NOT_EXIST = \u8BD5\u5242\u89C4\u683C\u4E0D\u5B58\u5728
INVALID_REAGENT_BRAND_DICT = \u65E0\u6548\u7684\u8BD5\u5242\u54C1\u724C\u5B57\u5178\u503C
INVALID_REAGENT_UNIT_DICT = \u65E0\u6548\u7684\u8BD5\u5242\u8BA1\u91CF\u5355\u4F4D\u5B57\u5178\u503C
INVALID_REAGENT_STORAGE_CONDITION_DICT = \u65E0\u6548\u7684\u8BD5\u5242\u5B58\u50A8\u6761\u4EF6\u5B57\u5178\u503C

CONSUMABLE_NOT_EXIST = \u8017\u6750\u4E0D\u5B58\u5728
CONSUMABLE_HAS_SPECIFICATIONS = \u8017\u6750\u5B58\u5728\u89C4\u683C\u6570\u636E\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
CONSUMABLE_SPECIFICATION_NOT_EXIST = \u8017\u6750\u89C4\u683C\u4E0D\u5B58\u5728
INVALID_CONSUMABLE_BRAND_DICT = \u65E0\u6548\u7684\u8017\u6750\u54C1\u724C\u5B57\u5178\u503C
INVALID_CONSUMABLE_UNIT_DICT = \u65E0\u6548\u7684\u8017\u6750\u8BA1\u91CF\u5355\u4F4D\u5B57\u5178\u503C
INVALID_CONSUMABLE_STORAGE_CONDITION_DICT = \u65E0\u6548\u7684\u8017\u6750\u5B58\u50A8\u6761\u4EF6\u5B57\u5178\u503C
INVALID_CONSUMABLE_MATERIAL_DICT = \u65E0\u6548\u7684\u8017\u6750\u6750\u8D28\u5B57\u5178\u503C

FLOW_DEFINITION_NOT_EXIST = \u6D41\u7A0B\u4E0D\u5B58\u5728