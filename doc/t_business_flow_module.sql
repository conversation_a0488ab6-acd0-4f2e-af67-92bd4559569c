/*
 Navicat Premium Dump SQL

 Source Server         : lanhu-mysql8-test
 Source Server Type    : MySQL
 Source Server Version : 80032 (8.0.32)
 Source Host           : ***************:5506
 Source Schema         : lims

 Target Server Type    : MySQL
 Target Server Version : 80032 (8.0.32)
 File Encoding         : 65001

 Date: 19/06/2025 09:43:41
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_business_flow_module
-- ----------------------------
DROP TABLE IF EXISTS `t_business_flow_module`;
CREATE TABLE `t_business_flow_module` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(100) DEFAULT NULL COMMENT '模块或类型名称',
  `sort_by` int DEFAULT NULL COMMENT '排序',
  `parent_id` bigint DEFAULT NULL COMMENT '模块的pid为0，类型的pid为响应模块的id',
  `create_by` bigint DEFAULT NULL COMMENT '创建人id',
  `create_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '修改人id',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
  `update_name` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `is_effect` int DEFAULT '0' COMMENT '0 正常，1 删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1103 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of t_business_flow_module
-- ----------------------------
BEGIN;
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1, '静态数据模块', 0, 0, 0, '系统', '2025-06-18 13:39:00', 0, '2025-06-18 13:39:08', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (2, '委托单模块', 1, 0, 0, '系统', '2025-06-18 13:39:00', 0, '2025-06-18 13:39:08', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (3, '样本模块', 2, 0, 0, '系统', '2025-06-18 13:39:00', 0, '2025-06-18 13:39:08', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (4, '检测模块', 3, 0, 0, '系统', '2025-06-18 13:39:00', 0, '2025-06-18 13:39:08', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (5, '报告模块', 4, 0, 0, '系统', '2025-06-18 13:39:00', 0, '2025-06-18 13:39:08', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (6, '物资模块', 5, 0, 0, '系统', '2025-06-18 13:39:00', 0, '2025-06-18 13:39:08', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (7, '资源模块', 6, 0, 0, '系统', '2025-06-18 13:39:00', 0, '2025-06-18 13:39:08', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (8, '文件模块', 7, 0, 0, '系统', '2025-06-18 13:39:00', 0, '2025-06-18 13:39:08', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1000, '静态数据新增审批', 0, 1, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1001, '静态数据编辑审批', 1, 1, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1002, '静态数据删除审批', 2, 1, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1003, '静态数据新增版本审批', 3, 1, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1010, '委托单新增审批', 0, 2, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1011, '委托单编辑审批', 1, 2, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1012, '委托单终止审批', 2, 2, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1020, '样本销毁审批', 0, 3, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1030, '检测结果审批', 0, 4, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1040, '报告生成审批', 0, 5, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1050, '耗材入库审批', 0, 6, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1051, '耗材出库审批', 1, 6, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1060, '试剂入库审批', 2, 6, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1061, '试剂出库审批', 3, 6, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1070, '客户新增审批', 0, 7, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1071, '客户编辑审批', 1, 7, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1072, '客户删除审批', 2, 7, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1073, '客户黑名单审批', 3, 7, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1080, '外包商新增审批', 4, 7, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1081, '外包商编辑审批', 5, 7, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1082, '外包商删除审批', 6, 7, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1083, '外包商黑名单审批', 7, 7, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1090, '系统文件新增审批', 0, 8, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1091, '系统文件编辑审批', 1, 8, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1092, '系统文件删除审批', 2, 8, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1100, '模板文件新增审批', 3, 8, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1101, '模板文件编辑审批', 4, 8, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
INSERT INTO `t_business_flow_module` (`id`, `name`, `sort_by`, `parent_id`, `create_by`, `create_name`, `create_time`, `update_by`, `update_time`, `update_name`, `is_effect`) VALUES (1102, '模板文件删除审批', 5, 8, 0, '系统', '2025-06-10 10:14:12', 0, '2025-05-28 15:29:27', '系统', 0);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
