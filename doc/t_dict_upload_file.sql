/*
 Navicat Premium Dump SQL

 Source Server         : lanhu-mysql8-test
 Source Server Type    : MySQL
 Source Server Version : 80032 (8.0.32)
 Source Host           : ***************:5506
 Source Schema         : lims

 Target Server Type    : MySQL
 Target Server Version : 80032 (8.0.32)
 File Encoding         : 65001

 Date: 19/06/2025 09:42:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_dict_upload_file
-- ----------------------------
DROP TABLE IF EXISTS `t_dict_upload_file`;
CREATE TABLE `t_dict_upload_file` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件类型',
  `file_size` int DEFAULT NULL COMMENT '可上传文件数量',
  `file_max` int DEFAULT NULL COMMENT '可上传单个文件大小',
  `file_suffix` varchar(50) DEFAULT NULL COMMENT '文件后缀',
  `file_upload_dir` varchar(100) DEFAULT NULL COMMENT '上传目录',
  `file_acl` int DEFAULT NULL COMMENT '文件访问权限(0:直接访问1:签名访问 2:下载访问)',
  `url` varchar(255) DEFAULT NULL COMMENT '访问地址',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注说明',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb3 COMMENT='上传文件配置表';

-- ----------------------------
-- Records of t_dict_upload_file
-- ----------------------------
BEGIN;
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (1, 'execution_standard_attachment', 1, 52428800, 'pdf', '/opt/zkqy/zkqy-lims-admin/file/execution_standard_attachment', 2, NULL, '执行标准附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (2, 'detection_method_attachment', 1, 52428800, 'pdf', '/opt/zkqy/zkqy-lims-admin/file/detection_method_attachment', 2, NULL, '检测方法附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (3, 'detection_project_attachment', 1, 52428800, 'pdf', '/opt/zkqy/zkqy-lims-admin/file/detection_project_attachment', 2, NULL, '检测项目附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (4, 'template_file_attachment', 1, 52428800, 'pdf,xlsx', '/opt/zkqy/zkqy-lims-admin/file/template_file_attachment', 2, NULL, '模板文件附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (5, 'entrust_order_attachment', 10, 52428800, 'pdf,jpg,jpeg,png,xlsx', '/opt/zkqy/zkqy-lims-admin/file/entrust_order_attachment', 2, NULL, '委托单附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (6, 'sample_import_attachment', 1, 52428800, 'xlsx', '/opt/zkqy/zkqy-lims-admin/file/sample_import_attachment', 2, NULL, '样本导入附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (7, 'sample_attachment', 10, 52428800, 'jpg,jpeg,png', '/opt/zkqy/zkqy-lims-admin/file/sample_attachment', 2, NULL, '样本附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (8, 'experiment_record_attachment', 10, 52428800, 'jpg,jpeg,png', '/opt/zkqy/zkqy-lims-admin/file/experiment_record_attachment', 2, NULL, '实验记录附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (9, 'customer_attachment', 10, 52428800, 'pdf,jpg,jpeg,png', '/opt/zkqy/zkqy-lims-admin/file/customer_attachment', 2, NULL, '客户附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (10, 'outsourcer_attachment', 10, 52428800, 'pdf,jpg,jpeg,png', '/opt/zkqy/zkqy-lims-admin/file/outsourcer_attachment', 2, NULL, '外包商附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (11, 'system_file_attachment', 10, 52428800, 'pdf,xlsx,csv', '/opt/zkqy/zkqy-lims-admin/file/system_file_attachment', 2, NULL, '系统文件附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (12, 'reagent_attachment', 10, 52428800, 'pdf,jpg,jpeg,png', '/opt/zkqy/zkqy-lims-admin/file/reagent_attachment', 2, NULL, '试剂附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (13, 'consumable_attachment', 10, 52428800, 'pdf,jpg,jpeg,png', '/opt/zkqy/zkqy-lims-admin/file/consumable_attachment', 2, NULL, '耗材附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (14, 'equipment_attachment', 10, 52428800, 'pdf,jpg,jpeg,png', '/opt/zkqy/zkqy-lims-admin/file/equipment_attachment', 2, NULL, '仪器附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (15, 'user_icon', 1, 5242880, 'jpg,jpeg,png', '/opt/zkqy/zkqy-lims-admin/file/user_icon', 0, NULL, '用户头像');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (16, 'detection_result_attachment', 10, 52428800, 'pdf,jpg,jpeg,png,xlsx,csv', '/opt/zkqy/zkqy-lims-admin/file/detection_result_attachment', 2, NULL, '检测结果附件');
INSERT INTO `t_dict_upload_file` (`id`, `file_type`, `file_size`, `file_max`, `file_suffix`, `file_upload_dir`, `file_acl`, `url`, `remark`) VALUES (17, 'detection_report_attachment', 10, 52428800, 'xlsx,csv', '/opt/zkqy/zkqy-lims-admin/file/detection_report_attachment', 2, NULL, '检测报告附件');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
